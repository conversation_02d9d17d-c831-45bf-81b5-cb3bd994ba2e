arguments=--init-script /home/<USER>/.config/Code/User/globalStorage/redhat.java/1.43.1/config_linux/org.eclipse.osgi/58/0/.cp/gradle/init/init.gradle --init-script /home/<USER>/.config/Code/User/globalStorage/redhat.java/1.43.1/config_linux/org.eclipse.osgi/58/0/.cp/gradle/protobuf/init.gradle
auto.sync=false
build.scans.enabled=false
connection.gradle.distribution=GRADLE_DISTRIBUTION(WRAPPER)
connection.project.dir=
eclipse.preferences.version=1
gradle.user.home=
java.home=/usr/lib/jvm/java-17-openjdk-amd64
jvm.arguments=
offline.mode=false
override.workspace.settings=true
show.console.view=true
show.executions.view=true
