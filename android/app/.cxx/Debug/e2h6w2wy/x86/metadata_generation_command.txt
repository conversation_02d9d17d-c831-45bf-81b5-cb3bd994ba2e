                        -H/home/<USER>/snap/flutter/common/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-<PERSON><PERSON><PERSON>OID_PLATFORM=android-24
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=/home/<USER>/Android/Sdk/ndk/26.3.11579264
-DCMAKE_ANDROID_NDK=/home/<USER>/Android/Sdk/ndk/26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=/home/<USER>/Android/Sdk/ndk/26.3.11579264/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/home/<USER>/Downloads/signify_signify 2 (1)/Luminator_gradle_issue/build/app/intermediates/cxx/Debug/e2h6w2wy/obj/x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/home/<USER>/Downloads/signify_signify 2 (1)/Luminator_gradle_issue/build/app/intermediates/cxx/Debug/e2h6w2wy/obj/x86
-DCMAKE_BUILD_TYPE=Debug
-B/home/<USER>/Downloads/signify_signify 2 (1)/Luminator_gradle_issue/android/app/.cxx/Debug/e2h6w2wy/x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2