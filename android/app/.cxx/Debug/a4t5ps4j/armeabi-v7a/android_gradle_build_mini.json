{"buildFiles": ["/home/<USER>/snap/flutter/common/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja", "-C", "/home/<USER>/Downloads/signify_signify 2 (1)/Luminator_gradle_issue/android/app/.cxx/Debug/a4t5ps4j/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja", "-C", "/home/<USER>/Downloads/signify_signify 2 (1)/Luminator_gradle_issue/android/app/.cxx/Debug/a4t5ps4j/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}