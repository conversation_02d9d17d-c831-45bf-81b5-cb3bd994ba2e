{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/home/<USER>/Android/Sdk/cmake/3.22.1/bin/cmake", "cpack": "/home/<USER>/Android/Sdk/cmake/3.22.1/bin/cpack", "ctest": "/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ctest", "root": "/home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": false, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-f9222e2bb45bfe62c0af.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-38d3a1e3e5d09deb0079.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-055f00b8de8acdab58ac.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-38d3a1e3e5d09deb0079.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-055f00b8de8acdab58ac.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-f9222e2bb45bfe62c0af.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}