plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw  GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

 def keystoreProperties = new Properties()	
   def keystorePropertiesFile = rootProject.file('key.properties')	
   if (keystorePropertiesFile.exists()) {	
       keystoreProperties.load(new FileInputStream(keystorePropertiesFile))	
   }

android {
    namespace "com.schnell.schnell_luminator.schnell_luminator"
    buildToolsVersion "35.0.0"
    compileSdkVersion 35
    ndkVersion flutter.ndkVersion

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        multiDexEnabled true
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.schnell.schnell_luminator.schnell_luminator"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-build-configuration.
        minSdkVersion 24
        versionCode 301
        targetSdkVersion 35
        versionName flutterVersionName
    }

    signingConfigs {	
       release {	
           keyAlias keystoreProperties['keyAlias']	
           keyPassword keystoreProperties['keyPassword']	
           storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null	
           storePassword keystoreProperties['storePassword']	
       }	
   }
    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.release
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'
    implementation 'com.android.support:multidex:1.0.3'
    implementation platform('com.google.firebase:firebase-bom:31.3.0')  
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.android.material:material:1.11.0'
}
