import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart' as ph;
import 'package:schnell_luminator/attendence_tracking.dart/user_tracking_controller.dart';
import 'package:schnell_luminator/location_selection/location_controller.dart';
import 'package:schnell_luminator/login_page/login_controller.dart';
import 'package:schnell_luminator/utils/constants.dart';
import 'package:schnell_luminator/utils/dialog_box.dart';
import 'package:schnell_luminator/utils/error_messages.dart';
import 'package:schnell_luminator/utils/utility.dart';
import '../utils/asset_folder.dart';
import '../utils/session.dart';

final splashController = ChangeNotifierProvider<SplashProvider>(
  (ref) => SplashProvider(),
);

class SplashProvider extends ChangeNotifier {
  late Timer timer;
  String _latestVersion = "";

  Future<dynamic> _fetchLatestAppVersion(context, ref) async {
    final FirebaseRemoteConfig remoteConfig = FirebaseRemoteConfig.instance;

    await remoteConfig.setConfigSettings(
      RemoteConfigSettings(
        fetchTimeout: const Duration(milliseconds: 10),
        minimumFetchInterval: const Duration(seconds: 1),
      ),
    );
    await remoteConfig.fetchAndActivate();
    _latestVersion = remoteConfig.getString('app_version');
    bool isUpdateRequired = remoteConfig.getBool('is_update_required');
    String releaseNotes = remoteConfig.getString('release_notes');
    Map<String, dynamic> releaseNotesMap = jsonDecode(releaseNotes);
    int currentAppVersion = int.parse(appVersion.split('.').join());
    int latestAppVersion = int.parse(_latestVersion.split('.').join());
    if (currentAppVersion < latestAppVersion) {
      if (isUpdateRequired) {
        Navigator.of(
          context,
        ).pushNamedAndRemoveUntil(updateRoute, (route) => false);
        return true;
      } else {
        await updatePopup(
          context,
          ref,
          ErrorMessages.updateAlert(latestVersion: _latestVersion),
          releaseNotesMap,
        );
      }
    }
  }

  Future<bool> checkAppVersion(BuildContext context, WidgetRef ref) async {
    await _fetchLatestAppVersion(context, ref);
    await ref.read(assetsController).initializeAssets();
    var data = await getUser();
    bool isLocationTrackingRequi = await isLocationTrackingRequired();
    if (isLocationTrackingRequi) {
      //auto logout after 24 hrs
      bool didAutoLogout =
          await ref.read(userTrackingController).autoLogout(context, ref);
      if (didAutoLogout) return false;
    }
    timer = Timer(const Duration(milliseconds: 2000), () async {
      const ph.Permission locationPermission = ph.Permission.location;
      const ph.Permission backgroundPermission = ph.Permission.locationAlways;
      bool locationStatus = false;
      bool isPermanentlyDenied = await locationPermission.isPermanentlyDenied;

      if (isPermanentlyDenied) {
        log(ErrorMessages.locationDeniedError);
        if (context.mounted) {
          permissionRequiredAlert(context, ErrorMessages.enableLocationAlert);
        }
      } else {
        var locationStatusResult = await locationPermission.request();

        if (locationStatusResult.isGranted || locationStatusResult.isLimited) {
          // Check for background permission if Android 11+
          if (await backgroundPermission.isDenied ||
              await backgroundPermission.isRestricted) {
            var backgroundStatusResult = await backgroundPermission.request();
            if (!backgroundStatusResult.isGranted) {
              log("Background location not granted");
              if (context.mounted) {
                await permissionRequiredAlert(
                    context, ErrorMessages.enableLocationAlert);
              }
            }
          }

          // Location permission granted
          locationStatus = true;

          if (locationStatus) {
            if (context.mounted) {
              if (data == '') {
                Navigator.of(
                  context,
                ).pushNamedAndRemoveUntil(loginRoute, (route) => false);
              } else {
                EasyLoading.dismiss();
                Utility.isConnected().then((value) async {
                  if (value) {
                    String userMail = await getUserName();
                    ref.read(loginController).setUserMail(userMail);
                    if (context.mounted) {
                      await ref
                          .read(locationController)
                          .getCustomerDetails(ref, context);
                      // Navigator.of(context).pushNamedAndRemoveUntil(
                      //     customerRoute, (route) => false);
                      // await ref.read(loginController).validateLogin(
                      //     context, ref, userData.username, userData.password);
                    }
                  } else {
                    if (context.mounted) {
                      await snackBar(
                        context,
                        ErrorMessages.offlineErrorTitle,
                        ErrorMessages.offlineErrorMessage,
                      );
                    }
                  }
                });
              }
            }
          }
        } else {
          if (context.mounted) {
            await permissionRequiredAlert(
                context, ErrorMessages.enableLocationAlert);
          }
          if (locationStatus == false) {
            if (context.mounted) {
              await checkAppVersion(context, ref);
            }
          }
        }
        log(locationStatus.toString());
      }
    });
    return Future.value(true);
  }

  String get latestVersion => _latestVersion;
}
