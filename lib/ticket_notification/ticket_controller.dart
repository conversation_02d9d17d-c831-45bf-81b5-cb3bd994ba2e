import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:schnell_luminator/attendence_tracking.dart/user_tracking_controller.dart';
import 'package:schnell_luminator/device_page/device_controller.dart';
import 'package:schnell_luminator/device_page/device_service.dart';
import 'package:schnell_luminator/ticket_notification/comment_model.dart';
import 'package:schnell_luminator/utils/dialog_box.dart';
import 'package:schnell_luminator/utils/error_messages.dart';
import 'package:schnell_luminator/utils/session.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:schnell_luminator/location_selection/location_controller.dart';
import '../login_page/login_controller.dart';
import 'ticket_model.dart';
import 'ticket_service.dart';

final ticketController =
    ChangeNotifierProvider<TicketProvider>((ref) => TicketProvider());

class TicketProvider extends ChangeNotifier {
  final TicketService _service = TicketService();
  final TextEditingController _commentController = TextEditingController();
  List<Ticket> _tickets = [];
  List<Ticket> _filteredTickets = [];
  int _totalElementsWithDuplicates = 0;
  late Ticket _selectedTicekt;
  List<Comments> _comments = [];
  String _searchText = '';
  int scrollCount = 1;
  String _selectedContextId = '';
  int _totalTicketsCount = 0;
  int _totalPageCount = 0;
  int _fetchedTicketCount = 0;
  String _ticketStatusValue = 'Ticket Status (ALL)';
  String _componentsChangedValue = 'Components Changed';
  String _issuesIdentifiedValue = 'Issues Identified';
  String _newTicketStatus = '';
  String _newComponentsChangedValue = '';
  String _newIssuesIdentifiedValue = '';
  bool _isComponentChangeFieldEnabled = false;
  String _eneteredComment = '';
  //For Ticket Creation
  // String _selectedDeviceType = 'Device/Asset Type*';
  String _selectedDeviceOrAssetName = 'Device / Asset Id';
  String _selectedDevice = '';
  String _location = '';
  String _deviceZone = '';
  String _deviceWard = '';
  String _deviceRegion = '';
  String _selectedPriority = 'Priority';
  String _selectedProblemType = 'Problem Type*';
  String _selectedStatus = 'Status';
  String _selectedAssignee = 'Assignee';
  String _selectedcomplainee = 'Complainee';
  String _selectedComplaineePhoneNo = '';
  String _enteredTicketCreationComments = '';
  String _deviceOrAssetType = "others";
  bool isTicketLampJson = false;
  bool _isTicketsOfSpecificDevice = false;

  List<String> _deviceNames = [];
  final TextEditingController _locController = TextEditingController();
  final TextEditingController _complaineePHController = TextEditingController();
  final TextEditingController _creationCommentsController =
      TextEditingController();
  final TextEditingController _autoCompletecontroller =
      TextEditingController(text: "device/asset id");
  Map? _originatorDetail;
  String _ticketDeviceId = '';

  final List _sortByCategories = [
    {'name': 'By Status', 'isSelected': true},
    {'name': 'By Priority', 'isSelected': false},
    {'name': 'By Date', 'isSelected': false},
    {'name': 'By Ticket Id', 'isSelected': false},
    {'name': 'By Device', 'isSelected': false},
  ];

  Map<String, String> ticketStatusMapping = {
    'OPEN': 'ACTIVE_UNACK',
    'ASSIGNED': 'ACTIVE_UNACK',
    'CLOSED': 'CLEARED_UNACK',
    'VISITED & NOT CLOSED': 'ACTIVE_ACK',
    'VERIFIED & CLOSED': 'CLEARED_ACK',
    'VISITED & CLOSED': 'CLEARED_ACK',
    'VERIFIED & NOT CLOSED': 'ACTIVE_ACK'
  };

  void changeComponenetChangedFieldValue() {
    _isComponentChangeFieldEnabled = !_isComponentChangeFieldEnabled;
    notifyListeners();
  }

  void updateTicketStatus(field, value) {
    switch (field) {
      case 'status':
        _newTicketStatus = value;
        break;
      case 'components':
        _componentsChangedValue = value;
        _newComponentsChangedValue = value;
        if (value != "Components Changed") {
          _newIssuesIdentifiedValue = value;
        }
        break;
      case 'issues':
        _issuesIdentifiedValue = value;
        _newIssuesIdentifiedValue = value;
        break;
      default:
    }
    log('Luminator: Selected ticket $field is $_newTicketStatus');
    notifyListeners();
  }

  void resetFieldValues(Ticket ticket) {
    _newTicketStatus = '';
    _newComponentsChangedValue =
        ticket.ticketDetails?['t_changed_component'] ?? '';
    _newIssuesIdentifiedValue =
        ticket.ticketDetails?['t_issue_identified'] ?? '';
    _isComponentChangeFieldEnabled = false;
    _componentsChangedValue = 'Components Changed';
    _issuesIdentifiedValue = 'Issues Identified';
    _eneteredComment = '';
    _commentController.text = '';
  }

  void updateSelectedTicket(Ticket ticket) {
    _selectedTicekt = ticket;
  }

  bool isLessThanOneDay(int milliseconds) {
    DateTime currentTime = DateTime.now();
    DateTime targetTime = DateTime.fromMillisecondsSinceEpoch(milliseconds);
    Duration difference = currentTime.difference(targetTime);
    return difference.inDays < 1;
  }

  String getTicketStatus(
      String? ticketStatus, bool? verified, String? assignee) {
    if (ticketStatus == null || verified == null) return '';
    final String upperCaseTicketStatus = ticketStatus.toUpperCase();
    if (upperCaseTicketStatus == 'ACTIVE_UNACK') {
      if ((assignee == null || assignee == "") && !verified) {
        return 'Open';
      } else if ((assignee != null || assignee != "") && !verified) {
        return 'Assigned';
      }
    } else if (upperCaseTicketStatus == 'CLEARED_UNACK') {
      return 'Closed';
    } else if (upperCaseTicketStatus == 'ACTIVE_ACK') {
      if (!verified) {
        return 'Visited & not closed';
      } else {
        return 'Verified & not closed';
      }
    } else if (upperCaseTicketStatus == 'CLEARED_ACK') {
      if (verified) {
        return 'Verified & closed';
      } else {
        return 'Visited & closed';
      }
    }
    return '';
  }

  String getDuration(String timeStamp) {
    int milliseconds = int.parse(timeStamp) * 1000;
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(milliseconds);
    String timeAgo = timeago.format(dateTime, locale: 'en_short');
    return timeAgo;
  }

  Future<void> getTickets(context, ref,
      {bool isScroll = false,
      String entityTypeForSpecificDeviceTicket =
          'ASSET', // required only for fetching tickets under specific device/asset
      String entityIdForSpecificDeviceTicket =
          '', // required only for fetching tickets under specific device/asset
      String selectedRegion =
          '' //(required only for fetching all the tickets)based on this region name, 'CUSTOMER' or 'ASSET' params validation have been done in api service
      }) async {
    String selectedEntityId = _isTicketsOfSpecificDevice
        ? entityIdForSpecificDeviceTicket
        : selectedContextId; // this condition is for : to fetch specific device/ asset ticket - asset/device Id should passed as params, to list all tickets under specific context - contextId must pass    // EasyLoading.show(status: 'loading...', dismissOnTap: false);

    String selectedEntityType = _isTicketsOfSpecificDevice
        ? entityTypeForSpecificDeviceTicket
        : selectedRegion == ''
            ? 'CUSTOMER'
            : 'ASSET'; // the above condition is for : customerOrAsset variable is required only when fetching all tickets, not when retrieving a single ticket.

    setScrollCount(isScroll ? scrollCount : 1);
    try {
      var res = await _service.getDefaultTicketService(
          context, ref, selectedEntityType, selectedEntityId,
          pageCount: isScroll ? scrollCount : 0);
      var ticketResponse = res['data'];
      _totalTicketsCount = res['totalElements'];
      _totalPageCount = res['totalPages'];
      _totalElementsWithDuplicates = res['totalElementsWithDuplicates'];
      setScrollCount(ticketResponse.isNotEmpty
          ? (isScroll ? scrollCount + 1 : 1)
          : scrollCount);
      _tickets = [];
      for (var ticket in ticketResponse) {
        //the below condition is for:
        //If it's a ticket of a specific device, exclude 'CLEARED_ACK' and 'CLEARED_UNACK' statuses(closed)
        // If it's not a ticket of a specific device:
        // 1. If it is a today's ticket, all status included
        // 2. If the ticket is not from today, exclude 'CLEARED_ACK' and 'CLEARED_UNACK' statuses(closed)
        bool isTicketValid = (_isTicketsOfSpecificDevice &&
                ticket['status'].toString().toUpperCase() != 'CLEARED_ACK' &&
                ticket['status'].toString().toUpperCase() != 'CLEARED_UNACK') ||
            (!_isTicketsOfSpecificDevice &&
                (isLessThanOneDay(
                        int.parse(ticket['createdTime'].toString())) ||
                    (ticket['status'].toString().toUpperCase() !=
                            'CLEARED_ACK' &&
                        ticket['status'].toString().toUpperCase() !=
                            'CLEARED_UNACK')));
        if (isTicketValid) {
          _tickets.add(Ticket(
              alarmId: ticket['id']['id'],
              originatorId: ticket['originator']['id'],
              originatorType: ticket['originator']['entityType'],
              ticketId: ticket['details']['ticket_name'],
              ticketStatus: getTicketStatus(
                  ticket['status'],
                  ticket['details']['t_verified'].toString().toUpperCase() ==
                      "TRUE",
                  ticket['assignee'] != null
                      ? ticket['assignee']['firstName']
                      : ''),
              deviceId: ticket['originatorName'],
              entityType: ticket['originator']['entityType'],
              complaineeType: ticket['details']['t_complainer'],
              assignee: ticket['assignee'] != null
                  ? ticket['assignee']['firstName']
                  : '',
              assigneeId: ticket['assigneeId'] != null
                  ? ticket['assigneeId']['id']
                  : '',
              priority: ticket['severity'],
              location: ticket['details']['t_location'],
              problemType: ticket['name'],
              comment: ticket['details']['t_comment'],
              deviceType: ticket['details']['t_entityType'],
              latitude: ticket['details']['t_latitude'].toString(),
              longitude: ticket['details']['t_longitude'].toString(),
              createdTime: ticket['createdTime'].toString(),
              ticketDetails: ticket['details'],
              lastModifiedDate: getDuration(ticket['ackTs'].toString())));
        }
        log('Luminator - Ticket added : $ticket');
      }
      _tickets.sort((a, b) =>
          int.parse(a.createdTime!).compareTo(int.parse(b.createdTime!)));
      // EasyLoading.dismiss();
      if (isScroll) {
        _filteredTickets = [..._filteredTickets, ..._tickets];
        _fetchedTicketCount = _filteredTickets.length;
        _tickets = _filteredTickets;
      } else {
        _filteredTickets = _tickets;
        _fetchedTicketCount = _filteredTickets.length;
      }
    } catch (e) {
      log(e.toString());
    }
  }

  void updateSearchText(String value) {
    _searchText = value;
    getFilteredTickets();
  }

  void updateSelectedAssetId(String assetId) {
    _ticketStatusValue = 'Ticket Status (ALL)';
    _selectedContextId = assetId;
  }

  getFilteredTickets() {
    if (_searchText != '') {
      _filteredTickets = _tickets
          .where((element) =>
              element.ticketId.toString().toLowerCase().contains(_searchText) ||
              element.ticketStatus
                  .toString()
                  .toLowerCase()
                  .contains(_searchText.toLowerCase()) ||
              element.deviceId
                  .toString()
                  .toLowerCase()
                  .contains(_searchText.toLowerCase()) ||
              element.problemType
                  .toString()
                  .toLowerCase()
                  .contains(_searchText.toLowerCase()) ||
              element.priority
                  .toString()
                  .toLowerCase()
                  .contains(_searchText.toLowerCase()) ||
              element.comment
                  .toString()
                  .toLowerCase()
                  .contains(_searchText.toLowerCase()) ||
              element.complaineeType
                  .toString()
                  .toLowerCase()
                  .contains(_searchText.toLowerCase()) ||
              element.location
                  .toString()
                  .toLowerCase()
                  .contains(_searchText.toLowerCase()))
          .toList();
    } else {
      _filteredTickets = _tickets;
    }
    notifyListeners();
  }

  void sortTickets(context, int index) async {
    Navigator.of(context, rootNavigator: true).pop();
    for (int i = 0; i < _sortByCategories.length; i++) {
      _sortByCategories[i]['isSelected'] = false;
    }
    switch (index) {
      case 0:
        _filteredTickets
            .sort((a, b) => a.ticketStatus!.compareTo(b.ticketStatus!));
        break;
      case 1:
        _filteredTickets.sort((a, b) => a.priority!.compareTo(b.priority!));
        break;
      case 2:
        _filteredTickets
            .sort((a, b) => b.createdTime!.compareTo(a.createdTime!));
        break;
      case 3:
        _filteredTickets
            .sort((a, b) => a.ticketId!.compareTo(b.ticketId ?? ''));
        break;
      case 4:
        _filteredTickets.sort((a, b) => a.deviceId!.compareTo(b.deviceId!));
        break;
      default:
        break;
    }
    log('selected index: $index');
    _sortByCategories[index]['isSelected'] = true;
    notifyListeners();
  }

  setScrollCount(int count) {
    scrollCount = count;
    notifyListeners();
  }

  void updateTicketDetailsWithReponse(data) async {
    _selectedTicekt.ticketDetails = data['details'];
    _selectedTicekt.ticketStatus = getTicketStatus(
        data['status'],
        data['details']['t_verified'].toString().toUpperCase() == "TRUE",
        data['assignee'] != null ? data['assignee']['firstName'] : '');
    notifyListeners();
  }

  Future<void> updateTicketDetails(BuildContext context, WidgetRef ref,
      Ticket ticket, enteredComment) async {
    EasyLoading.show(status: 'loading...', dismissOnTap: false);
    String ccmsAssetsId = ref.watch(deviceController).ccmsAssetsId;
    // String selectedRegion = ref.watch(deviceController).selectedRegion;

    String tickStatus = '';
    final Map<String, dynamic> postData = {
      "id": {"entityType": "ALARM", "id": ticket.alarmId},
      "originator": {
        "entityType": ticket.originatorType,
        "id": ticket.originatorId
      },
      "details": ticket.ticketDetails,
      "severity": ticket.priority
    };

    if (ticket.assigneeId != '') {
      postData["assigneeId"] = {"entityType": "USER", "id": ticket.assigneeId};
    }

    if (_newTicketStatus != '') {
      String statusUpperCase = _newTicketStatus.toUpperCase();
      if (statusUpperCase == "ASSIGNED") {
        tickStatus = 'ACTIVE_UNACK';
        postData['details']["t_verified"] = false;
        postData["acknowledged"] = false;
        postData["cleared"] = false;
      } else if (statusUpperCase == "CLOSED") {
        tickStatus = 'CLEARED_UNACK';
        postData['details']["t_verified"] = false;
        postData["acknowledged"] = false;
        postData["cleared"] = true;
        postData['clearTs'] = DateTime.now().millisecondsSinceEpoch;
      } else if (statusUpperCase == "VISITED & CLOSED") {
        tickStatus = 'CLEARED_ACK';
        postData['details']["t_verified"] = false;
        postData["acknowledged"] = true;
        postData["cleared"] = true;
        postData['ackTs'] = DateTime.now().millisecondsSinceEpoch;
        postData['clearTs'] = DateTime.now().millisecondsSinceEpoch;
      } else if (statusUpperCase == "VISITED & NOT CLOSED") {
        tickStatus = 'ACTIVE_ACK';
        postData['details']["t_verified"] = false;
        postData["acknowledged"] = true;
        postData["cleared"] = false;
        postData['ackTs'] = DateTime.now().millisecondsSinceEpoch;
      } else if (statusUpperCase == "VERIFIED & OPENED") {
        tickStatus = 'ACTIVE_ACK';
        postData['details']["t_verified"] = true;
        postData["acknowledged"] = true;
        postData["cleared"] = false;
        postData['ackTs'] = DateTime.now().millisecondsSinceEpoch;
      } else if (statusUpperCase == "VERIFIED & CLOSED") {
        tickStatus = 'CLEARED_ACK';
        postData['details']["t_verified"] = true;
        postData["acknowledged"] = true;
        postData["cleared"] = true;
        postData['ackTs'] = DateTime.now().millisecondsSinceEpoch;
        postData['clearTs'] = DateTime.now().millisecondsSinceEpoch;
      }
      postData['status'] = tickStatus;
    }

    if (_isComponentChangeFieldEnabled) {
      postData['details']["t_changed_component"] = _newComponentsChangedValue;
      postData['details']["t_issue_identified"] = _newIssuesIdentifiedValue;
    }

    try {
      log(postData.toString());
      bool isLocationTrackingRequi = await isLocationTrackingRequired();
      var res = await _service.updateTicketService(context, ref, postData,
          updateTicketDetailsWithReponse, enteredComment);
      if (res == 200) {
        // Track "Components Changed" activity if applicable
        if (isLocationTrackingRequi) {
          if (_isComponentChangeFieldEnabled) {
            await ref.read(userTrackingController).activityLocationTracking(
                  ref,
                  '$_newComponentsChangedValue Components Changed',
                  ticket.deviceId!,
                  entityType: ticket.deviceType,
                  isTicket: true,
                );
          }
        }
        EasyLoading.dismiss();
        await successTick(context, ErrorMessages.ticketSuccessAlert);
        Navigator.of(context, rootNavigator: true).pop();
      }
      // Track "status updated" activity if applicable

      if (isLocationTrackingRequi) {
        if (_newTicketStatus.isNotEmpty) {
          ref.read(userTrackingController).activityLocationTracking(
                ref,
                _newTicketStatus,
                ticket.ticketDetails!['ticket_name'],
                entityType: ticket.deviceType,
                isTicket: true,
              );
        }
      }

      if (_isTicketsOfSpecificDevice) {
        if (context.mounted) {
          ref.read(ticketController).getTickets(context, ref,
              entityTypeForSpecificDeviceTicket: 'ASSET',
              entityIdForSpecificDeviceTicket: ccmsAssetsId);
        }
      }
    } catch (e) {
      log(e.toString());
    }
  }

  Future<void> getTicketsBasedOnStatus(
      BuildContext context, WidgetRef ref, String status,
      [bool isScroll = false]) async {
    String selectedRegion = ref.watch(locationController).selectedRegion;
    setScrollCount(isScroll ? scrollCount : 1);
    if (status.toString().toUpperCase() != 'TICKET STATUS (ALL)') {
      var res = await _service.getTicketService(
          context, ref, selectedRegion, selectedContextId,
          searchText: ticketStatusMapping[status.toUpperCase()]!,
          pageCount: isScroll ? scrollCount : 0);
      _totalTicketsCount = res['totalElements'];
      log('total tickets count based on status: $status - $totalTicketsCount');
      _totalPageCount = res['totalPages'];
      List<Map> ticketResponse = List<Map>.from(res['data']);

      setScrollCount(ticketResponse.isNotEmpty
          ? (isScroll ? scrollCount + 1 : 1)
          : scrollCount);

      _tickets = ticketResponse.where((ticket) {
        bool isVerified =
            ticket['details']['t_verified'].toString().toUpperCase() == "TRUE";
        if ((ticket['status'].toString().toUpperCase() != 'CLEARED_ACK' ||
            ticket['status'].toString().toUpperCase() != 'CLEARED_UNACK')) {
          switch (status.toUpperCase()) {
            case 'OPEN':
              return !isVerified && ticket['assignee'] == null;
            case 'ASSIGNED':
              return !isVerified && ticket['assignee'] != null;
            case 'CLOSED':
            case 'VISITED & CLOSED':
            case 'VISITED & NOT CLOSED':
              return !isVerified;
            case 'VERIFIED & NOT CLOSED':
              return isVerified;
            default:
              return false;
          }
        }
        return false;
      }).map((ticket) {
        return Ticket(
          alarmId: ticket['id']['id'],
          originatorId: ticket['originator']['id'],
          originatorType: ticket['originator']['entityType'],
          ticketId: ticket['details']['ticket_name'],
          ticketStatus: getTicketStatus(
            ticket['status'],
            ticket['details']['t_verified'].toString().toUpperCase() == "TRUE",
            ticket['assignee'] != null ? ticket['assignee']['firstName'] : '',
          ),
          deviceId: ticket['originatorName'],
          entityType: ticket['originator']['entityType'],
          complaineeType: ticket['assignee'] == null
              ? ''
              : ticket['details']['t_complainer'],
          priority: ticket['severity'],
          assignee:
              ticket['assignee'] != null ? ticket['assignee']['firstName'] : '',
          assigneeId:
              ticket['assigneeId'] != null ? ticket['assigneeId']['id'] : '',
          location: ticket['details']['t_location'],
          problemType: ticket['name'],
          comment: ticket['details']['t_comment'],
          deviceType: ticket['details']['t_entityType'],
          latitude: ticket['details']['t_latitude'].toString(),
          longitude: ticket['details']['t_longitude'].toString(),
          createdTime: ticket['createdTime'].toString(),
          ticketDetails: ticket['details'],
          lastModifiedDate: getDuration(ticket['ackTs'].toString()),
        );
      }).toList();
      if (isScroll) {
        _filteredTickets = [..._filteredTickets, ..._tickets];
        _fetchedTicketCount = _filteredTickets.length;
        _tickets = _filteredTickets;
      } else {
        _filteredTickets = _tickets;
        _fetchedTicketCount = _filteredTickets.length;
      }
    } else {
      await ref.read(ticketController).isTicketsOfSpecificDeviceOrAsset(false);
      if (context.mounted) {
        await getTickets(context, isScroll, selectedRegion: selectedRegion);
      }
    }
    _ticketStatusValue = status;
    EasyLoading.dismiss();
    notifyListeners();
  }

  textFieldUpdate(eneteredComment) {
    _eneteredComment = eneteredComment;
    notifyListeners();
  }

  void addCommnets(responseData) {
    _comments.insert(0, Comments.fromJson(responseData));
    _commentController.clear();
    _eneteredComment = '';
    log(_comments.toString());
    notifyListeners();
  }

  Future<void> updateTicketComment(BuildContext context, WidgetRef ref,
      Ticket ticket, String comment) async {
    EasyLoading.show(status: 'loading...', dismissOnTap: false);
    final Map<String, dynamic> postDataForDBActivity = {
      "alarmId": {"id": ticket.alarmId, "entityType": "ALARM"},
      "comment": {"text": comment}
    };

    final Map<String, dynamic> postDataForDetailsBlock = {
      "id": {"entityType": "ALARM", "id": ticket.alarmId},
      "originator": {
        "entityType": ticket.originatorType,
        "id": ticket.originatorId
      },
      "details": ticket.ticketDetails,
      "severity": ticket.priority
    };
    if (_eneteredComment != '') {
      postDataForDetailsBlock['details']["t_comment"] = _eneteredComment;
    }

    try {
      log(postDataForDBActivity.toString());
      await _service.updateTicketCommentService(context, ref, ticket.alarmId!,
          postDataForDBActivity, postDataForDetailsBlock, addCommnets);
      bool isLocationTrackingRequi = await isLocationTrackingRequired();
      if (isLocationTrackingRequi) {
        ref.read(userTrackingController).activityLocationTracking(
            ref, 'Comments Updation', ticket.ticketId,
            entityType: ticket.ticketDetails!['t_entityType'], isTicket: true);
      }
    } catch (e) {
      log(e.toString());
    }
  }

  void saveCommnets(responseData) {
    _comments = [];
    List commentsList = responseData;
    for (var e in commentsList) {
      _comments.add(Comments.fromJson(e));
    }
  }

  Future<void> getTicketComments(context, ref, String alarmId) async {
    EasyLoading.show(status: 'loading...', dismissOnTap: false);
    try {
      await _service.getCommentsService(context, ref, alarmId, saveCommnets);
      log(_comments.toString());
    } catch (e) {
      log(e.toString());
      EasyLoading.dismiss();
      showSnackBar(ref, context, ErrorMessages.tryAgainError);
    }
  }

  //For Ticket Creation
  void saveDevices(responseData) {
    _deviceNames = [];
    List deviceNamesList = responseData;
    for (var e in deviceNamesList) {
      _deviceNames.add(e["latest"]["ENTITY_FIELD"]["name"]["value"]);
    }
    notifyListeners();
  }

  isTicketsOfSpecificDeviceOrAsset(value) {
    _isTicketsOfSpecificDevice = value;
    notifyListeners();
  }

  updateSelectedFields(
      context, ref, String? value, String? fieldName, bool isDeviceSelection) {
    String usermail = ref.watch(loginController).userMail;
    String hubOrCcms = ref.watch(deviceController).hubOrCcms;
    String ccmsAssetName = ref.watch(deviceController).ccmsAssetName;
    String ccmsLandmark = ref.watch(deviceController).ccmsLandmark;

    if (fieldName == "deviceType") {
      _deviceOrAssetType = value!;
      _deviceNames = [];
    } else if (fieldName == "Device / Asset Id") {
      _selectedDeviceOrAssetName = value!;
      getTicketDeviceId(context, ref);
    } else if (fieldName == "location") {
      _location = value!;
    } else if (fieldName == "priority") {
      _selectedPriority = value!;
    } else if (fieldName == "problemType") {
      _selectedProblemType = value!;
    } else if (fieldName == "status") {
      _selectedStatus = value!;
    } else if (fieldName == "complainee") {
      _selectedcomplainee = value!;
    } else if (fieldName == "complaineePH") {
      _selectedComplaineePhoneNo = value!;
    } else if (fieldName == "comments") {
      _enteredTicketCreationComments = value!;
    } else if (fieldName == "reset") {
      _creationCommentsController.text = '';
      _autoCompletecontroller.clear();
      _locController.text = '';
      _deviceOrAssetType = 'others';
      _selectedDevice = '';
      _location = '';
      _deviceZone = '';
      _deviceWard = '';
      _deviceRegion = '';
      _selectedPriority = 'Priority';
      _selectedProblemType = 'Problem Type*';
      _selectedStatus = 'Status';
      _selectedAssignee = usermail;
      _selectedcomplainee = 'Complainee';
      _selectedComplaineePhoneNo = '';
      _enteredTicketCreationComments = '';
      _deviceNames = [];
    } else if (fieldName == "maintenanceTickets") {
      _creationCommentsController.text = '';
      _autoCompletecontroller.text = ccmsAssetName;
      _selectedDeviceOrAssetName = ccmsAssetName;
      getTicketDeviceId(context, ref);
      _locController.text = ccmsLandmark;
      _deviceOrAssetType = hubOrCcms;
      _selectedDevice = '';
      _location = ccmsLandmark;
      _selectedPriority = 'Priority';
      _selectedProblemType = 'Problem Type*';
      _selectedStatus = 'Status';
      _selectedAssignee = usermail;
      _selectedcomplainee = 'Complainee';
      _selectedComplaineePhoneNo = '';
      _enteredTicketCreationComments = '';
      _deviceNames = [];
    }
    if (isDeviceSelection) {
      _creationCommentsController.text = '';
      _autoCompletecontroller.clear();
      _locController.text = '';
      _selectedDevice = '';
      _location = '';
      _deviceZone = '';
      _deviceWard = '';
      _deviceRegion = '';
      _selectedPriority = 'Priority';
      _selectedProblemType = 'Problem Type*';
      _selectedStatus = 'Status';
      _selectedAssignee = 'Assignee';
      _selectedcomplainee = 'Complainee';
      _selectedComplaineePhoneNo = '';
      _enteredTicketCreationComments = '';
      _deviceNames = [];
    }
    notifyListeners();
  }

  Future<void> getDeviceType(
    WidgetRef ref,
    BuildContext context,
    String scannedData,
  ) async {
    String deviceOrAsset;
    dynamic decodedData;
    try {
      decodedData = jsonDecode(scannedData);
      isTicketLampJson = true;
    } catch (e) {
      isTicketLampJson = false;
    }

    if (isTicketLampJson && decodedData is Map<String, dynamic>) {
      deviceOrAsset =
          '${decodedData['m']}-${decodedData['w']}-${decodedData['l']}-${decodedData['d']}-${decodedData['y']}-${decodedData['i']}';
    } else {
      deviceOrAsset = scannedData;
    }
    var res = await DeviceService()
        .deviceListSearchService(ref, context, deviceOrAsset);

    if (res == 1) {
      List deviceSearchList = ref.read(deviceController).deviceSearchList;
      _deviceOrAssetType = deviceSearchList[0]['type'];
    } else if (res == 404) {
      _deviceOrAssetType = "others";
    }
  }

  Future<void> getLpDetailsForLamp(
    WidgetRef ref,
    BuildContext context,
    String scannedData,
  ) async {
    dynamic decodedData;

    decodedData = jsonDecode(scannedData);

    scannedData =
        '${decodedData['m']}-${decodedData['w']}-${decodedData['l']}-${decodedData['d']}-${decodedData['y']}-${decodedData['i']}';

    var result =
        await _service.getLpDetailsForLampService(ref, context, scannedData);
    if (result != '') {
      saveDeivceLocation(result['lmpLocation'], result['lmpZone'],
          result['lmpWard'], result['lmpRegion']);
    }
  }

  Future<void> getDevices(
    context,
    ref,
  ) async {
    EasyLoading.show(status: 'loading...', dismissOnTap: false);
    Map postData = {
      "entityFields": [
        {"type": "ENTITY_FIELD", "key": "name"},
        {"type": "ENTITY_FIELD", "key": "type"},
        {"type": "ENTITY_FIELD", "key": "label"}
      ],
      "latestValues": [],
      "pageLink": {
        "dynamic": true,
        "page": 0,
        "pageSize": 100000,
        "sortOrder": {"direction": "ASC"},
        "textSearch": ""
      }
    };
    if (_deviceOrAssetType == 'ilm' ||
        _deviceOrAssetType == 'gw' ||
        _deviceOrAssetType == 'ilm-4g') {
      postData["entityFilter"] = {
        "type": "deviceSearchQuery",
        "rootEntity": {"entityType": "ASSET", "id": _selectedContextId},
        "direction": "FROM",
        "fetchLastLevelOnly": false,
        "deviceTypes": [_deviceOrAssetType]
      };
    } else {
      postData["entityFilter"] = {
        "type": "assetSearchQuery",
        "rootEntity": {"entityType": "ASSET", "id": _selectedContextId},
        "direction": "FROM",
        "fetchLastLevelOnly": false,
        "assetTypes": [_deviceOrAssetType]
      };
    }
    try {
      await _service.getDevicesService(context, ref, postData, saveDevices);
      log(_deviceNames.toString());
    } catch (e) {
      log(e.toString());
      EasyLoading.dismiss();
      showSnackBar(ref, context, ErrorMessages.tryAgainError);
    }
  }

  void saveDeivceLocation(
      String value, String zone, String ward, String region) {
    _location = value;
    _locController.text = value;
    _deviceZone = zone;
    _deviceWard = ward;
    _deviceRegion = region;
    notifyListeners();
  }

  Future<void> getTicketDeviceId(
    context,
    ref,
  ) async {
    _originatorDetail = await _service.getdeviceId(
        context, ref, _deviceOrAssetType, _selectedDeviceOrAssetName);
    _ticketDeviceId = _originatorDetail!['id'];
    if (!_isTicketsOfSpecificDevice) {
      if (_ticketDeviceId.isNotEmpty) {
        if (_deviceOrAssetType == "lamp") {
          getLpDetailsForLamp(ref, context, _selectedDeviceOrAssetName);
        } else {
          await _service.getTicketDevicesLocation(context, ref, _ticketDeviceId,
              _deviceOrAssetType, saveDeivceLocation);
        }
      } else {
        saveDeivceLocation("", "", "", "");
      }
    }
  }

  Future<dynamic> createTicketDetails(BuildContext context, WidgetRef ref,
      String customerId, String ward, usermail) async {
    EasyLoading.show(status: 'loading...', dismissOnTap: false);
    String ccmsAssetsId = ref.watch(deviceController).ccmsAssetsId;
    String selectedRegion = ref.watch(deviceController).selectedRegion;

    Map assigneeIdDetails = await _service.getUserId(context, ref, usermail);
    final Map<String, dynamic> postData = {
      "customerId": {"id": customerId, "entityType": "CUSTOMER"},
      "tenantId": {
        "id": "911947b0-752b-11ed-a1d5-c72869dcd693",
        "entityType": "TENANT"
      },
      "type": _selectedProblemType,
      "name": _selectedProblemType,
      "status": "ACTIVE_UNACK",
      "severity": "MAJOR",
      "acknowledged": "false",
      "cleared": "false",
      "assigneeId": assigneeIdDetails,
      "ackTs": 0,
      "clearTs": 0,
      "assignTs": 0,
      "details": {
        "t_ward": ward,
        "t_location": _location,
        "t_priority": "Major",
        "t_complainer": _selectedAssignee,
        "t_verified": false,
        "t_created_by": "",
        "t_type": "Manual",
        "t_entityType": _deviceOrAssetType,
        "t_latitude": "",
        "t_longitude": ""
      },
      "propagateToOwnerHierarchy": true,
      "propagate": true,
      "propagateToTenant": true,
      "propagateRelationTypes": [],
      "propagateToOwner": true
    };
    if (_ticketDeviceId.isNotEmpty) {
      postData["originator"] = _originatorDetail;
      postData["details"]["t_comment"] = _enteredTicketCreationComments;
    } else {
      postData["originator"] = {"entityType": "ASSET", "id": selectedContextId};
      postData["details"]["t_comment"] =
          "$_deviceOrAssetType: $_selectedDeviceOrAssetName. Comments: $_enteredTicketCreationComments.";
    }

    log(postData.toString());
    var res = await _service.createTicketService(context, ref, postData);

    if (res["status"] == 200) {
      bool isLocationTrackingRequi = await isLocationTrackingRequired();
      if (isLocationTrackingRequi) {
        ref.read(userTrackingController).activityLocationTracking(
            ref, 'Ticket Creation', res["ticketName"],
            entityType: _deviceOrAssetType, isTicket: true);
      }

      EasyLoading.dismiss();
      if (context.mounted) {
        await successTick(
            context, "Ticket ${res["ticketName"]} created successfully");
        if (context.mounted) {
          Navigator.of(context, rootNavigator: true).pop();
        }
      }
      if (_isTicketsOfSpecificDevice) {
        if (context.mounted) {
          ref.read(ticketController).getTickets(context, ref,
              entityTypeForSpecificDeviceTicket: 'ASSET',
              entityIdForSpecificDeviceTicket: ccmsAssetsId);
        }
      } else {
        if (context.mounted) {
          ref
              .read(ticketController)
              .getTickets(context, ref, selectedRegion: selectedRegion);
        }
      }
    } else if (res == 401) {
      EasyLoading.dismiss();
      if (context.mounted) {
        await tokenExpired(context, ref);
      }
    } else if (res == '500' ||
        (res is Map && (res["status"] == 500 || res["status"] == 503))) {
      EasyLoading.dismiss();
      if (context.mounted) {
        showSnackBar(ref, context, ErrorMessages.systemNotResponsiveError);
      }
    } else {
      EasyLoading.dismiss();
      if (context.mounted) {
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    }
  }

  TextEditingController get commentController => _commentController;
  TextEditingController get autoCompletecontroller => _autoCompletecontroller;
  String get selectedDeviceOrAssetName => _selectedDeviceOrAssetName;
  int get totalElementsWithDuplicates => _totalElementsWithDuplicates;
  String get eneteredComment => _eneteredComment;
  String get newComponentsChangedValue => _newComponentsChangedValue;
  String get newIssuesIdentifiedValue => _newIssuesIdentifiedValue;
  String get newTicketStatus => _newTicketStatus;
  String get searchText => _searchText;
  int get fetchedTicketCount => _fetchedTicketCount;
  int get totalTicketsCount => _totalTicketsCount;
  int get totalPageCount => _totalPageCount;
  String get ticketStatusValue => _ticketStatusValue;
  String get componentsChangedValue => _componentsChangedValue;
  String get issuesIdentifiedValue => _issuesIdentifiedValue;
  List<Ticket> get tickets => _tickets;
  List get sortByCategories => _sortByCategories;
  Ticket get selectedTicekt => _selectedTicekt;
  List<Ticket> get filteredTickets => _filteredTickets;
  List<Comments> get comments => _comments;
  bool get isComponentChangeFieldEnabled => _isComponentChangeFieldEnabled;
  bool get isTicketsOfSpecificDevice => _isTicketsOfSpecificDevice;
  //For Ticket Creation
  TextEditingController get locController => _locController;
  TextEditingController get complaineePHController => _complaineePHController;
  TextEditingController get creationCommentsController =>
      _creationCommentsController;
  String get deviceOrAssetType => _deviceOrAssetType;
  String get selectedDevice => _selectedDevice;
  String get location => _location;
  String get deviceZone => _deviceZone;
  String get deviceWard => _deviceWard;
  String get deviceRegion => _deviceRegion;
  String get selectedPriority => _selectedPriority;
  String get selectedProblemType => _selectedProblemType;
  String get selectedStatus => _selectedStatus;
  String get selectedContextId => _selectedContextId;
  String get selectedAssignee => _selectedAssignee;
  String get selectedcomplainee => _selectedcomplainee;
  String get selectedComplaineePhoneNo => _selectedComplaineePhoneNo;
  String get enteredTicketCreationComments => _enteredTicketCreationComments;
  List<String> get deviceNames => _deviceNames;
  String get ticketDeviceId => _ticketDeviceId;
}
