class Ticket {
  String? alarmId;
  String? originatorId;
  String? originatorType;
  String? ticketStatus;
  String? priority;
  String? assignee;
  String? assigneeId;
  String? lastModifiedDate;
  String? ticketId;
  String? complaineeType;
  String? location;
  String? entityType; //DEVICE/ASSET for api posting purpose
  String? deviceId;
  String? problemType;
  String? comment;
  String? deviceType; //lamp, ilm, ge, ccms, pole..etc,
  String? latitude;
  String? longitude;
  String? createdTime;
  Map? ticketDetails;

  Ticket({
    this.alarmId,
    this.originatorId,
    this.originatorType,
    this.ticketStatus,
    this.priority,
    this.assignee,
    this.assigneeId,
    this.lastModifiedDate,
    this.ticketId,
    this.complaineeType,
    this.location,
    this.entityType,
    this.deviceId,
    this.problemType,
    this.comment,
    this.deviceType,
    this.latitude,
    this.longitude,
    this.createdTime,
    this.ticketDetails,
  });

  Ticket.fromJson(Map<String, dynamic> json) {
    alarmId = json['alarmId'];
    originatorId = json['originatorId'];
    originatorType = json['originatorType'];
    ticketStatus = json['ticketStatus'];
    priority = json['priority'];
    assignee = json['assignee'];
    assigneeId = json['assigneeId'];
    lastModifiedDate = json['lastModifiedDate'];
    ticketId = json['ticketId'];
    complaineeType = json['complaineeType'];
    location = json['location'];
    entityType = json['deviceType'];
    deviceId = json['deviceId'];
    problemType = json['problemType'];
    comment = json['comments'];
    deviceType = json['type'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    createdTime = json['createdTime'];
    ticketDetails = json['details'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['alarmId'] = alarmId;
    data['originatorId'] = originatorId;
    data['originatorType'] = originatorType;
    data['ticketStatus'] = ticketStatus;
    data['priority'] = priority;
    data['assignee'] = assignee;
    data['assigneeId'] = assigneeId;
    data['lastModifiedDate'] = lastModifiedDate;
    data['ticketId'] = ticketId;
    data['complaineeType'] = complaineeType;
    data['location'] = location;
    data['deviceType'] = entityType;
    data['deviceId'] = deviceId;
    data['problemType'] = problemType;
    data['comments'] = comment;
    data['createdTime'] = createdTime;
    data['type'] = deviceType;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['details'] = ticketDetails;
    return data;
  }
}
