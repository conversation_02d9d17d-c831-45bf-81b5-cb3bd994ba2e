import 'dart:convert';
import 'dart:developer' as dev;
import 'dart:math';
import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:schnell_luminator/utils/dio_client.dart';
import 'package:schnell_luminator/utils/error_messages.dart';
import '../device_page/device_controller.dart';
import '../utils/constants.dart';
import '../utils/dialog_box.dart';

class TicketService {
  Future<dynamic> getDefaultTicketService(
    context,
    ref,
    String selectedEntityType,
    String selectedEntityId, {
    String searchText = '',
    int pageCount = 0,
  }) async {
    Dio dio = DioClient.dio;
    DateTime now = DateTime.now();
    DateTime startOfToday = DateTime(now.year, now.month, now.day);
    DateTime endOfToday = DateTime(now.year, now.month, now.day + 1);
    int startTimestamp = startOfToday.millisecondsSinceEpoch;
    int endTimestamp = endOfToday.millisecondsSinceEpoch;

    try {
      Response statusResponse = await dio.get(
        '$ticketURL/api/v2/alarm/$selectedEntityType/$selectedEntityId?pageSize=10000&page=$pageCount&textSearch=ACTIVE_UNACK&sortProperty=createdTime&sortOrder=ASC',
        options: Options(
          contentType: Headers.formUrlEncodedContentType,
        ),
      );

      Response dateResponse = await dio.get(
        '$ticketURL/api/v2/alarm/$selectedEntityType/$selectedEntityId?pageSize=10000&page=$pageCount&sortProperty=createdTime&sortOrder=ASC&startTime=$startTimestamp&endTime=$endTimestamp',
        options: Options(
          contentType: Headers.formUrlEncodedContentType,
        ),
      );

      if ((statusResponse.statusCode == 200 && statusResponse.data != '') &&
          (dateResponse.statusCode == 200 && dateResponse.data != '')) {
        var statusTickets = statusResponse.data["data"];
        var dateTickets = dateResponse.data["data"];
        var totalElementsWithDuplicates = statusResponse.data["totalElements"];
        var allTickets = [...statusTickets, ...dateTickets];
        var uniqueTickets = removeDuplicates(allTickets);
        var minTotalPages = min(
            int.parse(statusResponse.data["totalPages"].toString()),
            int.parse(dateResponse.data["totalPages"].toString()));
        var totalElements = uniqueTickets.length;
        var hasNext = statusResponse.data["hasNext"].toString() == 'true' &&
            dateResponse.data["hasNext"].toString() == 'true';
        return {
          "data": uniqueTickets,
          "totalPages": minTotalPages,
          "totalElements": totalElements,
          "totalElementsWithDuplicates": totalElementsWithDuplicates,
          "hasNext": hasNext
        };
      } else if ((statusResponse.data.containsKey('status') &&
              statusResponse.data['status'] != 200) &&
          (dateResponse.data.containsKey('status') &&
              dateResponse.data['status'] != 200)) {
        return statusResponse.data;
      } else {
        return 0;
      }
    } catch (e) {
      EasyLoading.dismiss();
      if (e is DioError) {
        if (e.error == 'Session expired. Please login again.') {
          await tokenExpired(context, ref);
        } else {
          dev.log(ErrorMessages.updateTicketServiceError);
        }
      } else {
        dev.log(ErrorMessages.updateTicketServiceTryAgainError);
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    }
  }

  Future<dynamic> getTicketService(
      context, ref, String selectedRegion, String assetId,
      {String searchText = '', int pageCount = 0}) async {
    Dio dio = DioClient.dio;
    String customerorAsset = selectedRegion == '' ? 'CUSTOMER' : 'ASSET';

    try {
      String url =
          '$ticketURL/api/v2/alarm/$customerorAsset/$assetId?pageSize=50&page=$pageCount&sortProperty=createdTime&sortOrder=ASC';
      if (searchText.isNotEmpty) {
        url += '&textSearch=$searchText';
      }
      Response response = await dio.get(
        url,
        options: Options(
          contentType: Headers.formUrlEncodedContentType,
        ),
      );
      if (response.statusCode == 200 && response.data != '') {
        dev.log('total tickets count based on status : ${response.data} ');
        return response.data;
      } else if (response.data.containsKey('status') &&
          response.data['status'] != 200) {
        return response.data;
      } else {
        return 0;
      }
    } catch (e) {
      EasyLoading.dismiss();
      if (e is DioError) {
        if (e.error == 'Session expired. Please login again.') {
          await tokenExpired(context, ref);
        } else {
          dev.log(ErrorMessages.updateTicketServiceError);
        }
      } else {
        dev.log(ErrorMessages.updateTicketServiceTryAgainError);
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    }
  }

  Future<dynamic> getUserId(context, ref, userMail) async {
    String encodedEmail = Uri.encodeComponent(userMail);
    Dio dio = DioClient.dio;
    Map<String, dynamic> headers = {
      'Content-Type': 'application/json',
      // 'X-Authorization': 'Bearer $token',
    };
    try {
      Response response = await dio.get(
        '$ticketURL/api/user/users?pageSize=100&page=0&textSearch=$encodedEmail',
        options: Options(headers: headers),
      );
      if (response.statusCode == 200 && response.data != '') {
        return response.data['data'][0]['id'];
      } else if (response.statusCode == 401) {
        EasyLoading.dismiss();
        await tokenExpired(context, ref);
      } else {
        EasyLoading.dismiss();
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    } catch (e) {
      EasyLoading.dismiss();
      if (e is DioError) {
        if (e.error == 'Session expired. Please login again.') {
          await tokenExpired(context, ref);
        } else {
          dev.log(ErrorMessages.updateTicketServiceError);
        }
      } else {
        dev.log(ErrorMessages.updateTicketServiceTryAgainError);
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    }
  }

  Future<dynamic> updateTicketService(
      context, ref, Map postData, Function callback, enteredComment) async {
    Dio dio = DioClient.dio;
    Map<String, dynamic> headers = {
      'Content-Type': 'application/json',
      // 'X-Authorization': 'Bearer $token',
    };
    try {
      Response response = await dio.post(
        '$ticketURL/api/alarm',
        data: postData,
        options: Options(headers: headers),
      );
      if (response.statusCode == 200 && response.data != '') {
        String alarmId = postData['id']['id'];
        final Map<String, dynamic> postDataForDBActivity = {
          "alarmId": {"id": alarmId, "entityType": "ALARM"},
          "comment": {"text": enteredComment}
        };
        callback(response.data);
        Response commentResponse = await dio.post(
          '$ticketURL/api/alarm/$alarmId/comment',
          data: postDataForDBActivity,
          options: Options(headers: headers),
        );
        return 200;
      } else if (response.statusCode == 401) {
        EasyLoading.dismiss();
        await tokenExpired(context, ref);
      } else {
        EasyLoading.dismiss();
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    } catch (e) {
      EasyLoading.dismiss();
      if (e is DioError) {
        if (e.error == 'Session expired. Please login again.') {
          await tokenExpired(context, ref);
        } else {
          dev.log(ErrorMessages.updateTicketServiceError);
        }
      } else {
        dev.log(ErrorMessages.updateTicketServiceTryAgainError);
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    }
  }

  Future<void> updateTicketCommentService(
      context,
      ref,
      String alarmId,
      Map postDataForDBActivity,
      Map postDataForDetailsBlock,
      Function addCommnets) async {
    Dio dio = DioClient.dio;
    Map<String, dynamic> headers = {
      'Content-Type': 'application/json',
      // 'X-Authorization': 'Bearer $token',
    };
    try {
      //Calling the POST api to update the entered comment
      Response commentResponse = await dio.post(
        '$ticketURL/api/alarm/$alarmId/comment',
        data: postDataForDBActivity,
        options: Options(headers: headers),
      );
      String? userId = commentResponse.data["userId"]["id"];
      if (commentResponse.statusCode == 200 && commentResponse.data != '') {
        //Calling the below POST api to update the recent comments on the 't_comments' field on details block
        await dio.post(
          '$ticketURL/api/alarm',
          data: postDataForDetailsBlock,
          options: Options(headers: headers),
        );
        //for calling the below api to get the firstname of the commenter
        Response userResponse = await dio.get(
          '$ticketURL/api/user/$userId',
          options: Options(headers: headers),
        );
        commentResponse.data["firstName"] = userResponse.data["firstName"];
        addCommnets(commentResponse.data);
        EasyLoading.dismiss();
        await successTick(context, "Comment updated successfully!");
      } else if (commentResponse.statusCode == 401) {
        EasyLoading.dismiss();
        await tokenExpired(context, ref);
      } else {
        EasyLoading.dismiss();
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    } catch (e) {
      EasyLoading.dismiss();
      if (e is DioError) {
        if (e.error == 'Session expired. Please login again.') {
          await tokenExpired(context, ref);
        } else {
          dev.log(ErrorMessages.updateTicketServiceError);
        }
      } else {
        dev.log(ErrorMessages.updateTicketServiceTryAgainError);
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    }
  }

  Future<dynamic> getCommentsService(
      context, ref, String alarmId, Function callback) async {
    Dio dio = DioClient.dio;

    try {
      String url =
          '$ticketURL/api/alarm/$alarmId/comment?pageSize=1000&page=0&sortProperty=createdTime&sortOrder=DESC';
      Response response = await dio.get(
        url,
        options: Options(
          contentType: Headers.formUrlEncodedContentType,
        ),
      );
      if (response.statusCode == 200 && response.data != '') {
        callback(response.data["data"]);
        EasyLoading.dismiss();
      } else if (response.statusCode == 401) {
        EasyLoading.dismiss();
        await tokenExpired(context, ref);
      } else {
        EasyLoading.dismiss();
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    } catch (e) {
      EasyLoading.dismiss();
      if (e is DioError) {
        if (e.error == 'Session expired. Please login again.') {
          await tokenExpired(context, ref);
        } else {
          dev.log(ErrorMessages.updateTicketServiceError);
        }
      } else {
        dev.log(ErrorMessages.updateTicketServiceTryAgainError);
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    }
  }

  Future<dynamic> getDevicesService(
      context, ref, Map postData, Function callback) async {
    Dio dio = DioClient.dio;

    try {
      String url = '$ticketURL/api/entitiesQuery/find';
      Response response = await dio.post(
        url,
        options: Options(
          contentType: Headers.jsonContentType,
        ),
        data: postData,
      );
      if (response.statusCode == 200 && response.data != '') {
        dev.log(response.data["data"].toString());
        callback(response.data["data"]);
        EasyLoading.dismiss();
      } else if (response.statusCode == 401) {
        EasyLoading.dismiss();
        await tokenExpired(context, ref);
      } else {
        EasyLoading.dismiss();
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    } catch (e) {
      EasyLoading.dismiss();
      if (e is DioError) {
        if (e.error == 'Session expired. Please login again.') {
          await tokenExpired(context, ref);
        } else {
          dev.log(ErrorMessages.updateTicketServiceError);
        }
      } else {
        dev.log(ErrorMessages.updateTicketServiceTryAgainError);
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    }
  }

  Future<dynamic> getTicketDevicesLocation(context, ref, String deviceId,
      String selectedDeviceType, Function callback) async {
    Dio dio = DioClient.dio;

    try {
      String type = selectedDeviceType == 'ilm' ||
              selectedDeviceType == 'gw' ||
              selectedDeviceType == 'ilm-4g'
          ? 'DEVICE'
          : 'ASSET';
      String fieldName = selectedDeviceType == 'ccms' ||
              selectedDeviceType == 'gw' ||
              selectedDeviceType == 'hub' ||
              selectedDeviceType == 'pole'
          ? 'location'
          : 'landmark';

      String url =
          '$ticketURL/api/plugins/telemetry/$type/$deviceId/values/attributes/SERVER_SCOPE?keys=$fieldName%2Cregion%2CzoneName%2CwardName%2CcustomerId';
      // http://iotpro.io/api/plugins/telemetry/DEVICE/995cb9c0-436a-11ef-b1fb-53c7aa78ce39/values/attributes/SERVER_SCOPE?keys=landmark%2Cregion%2CzoneName%2CwardName
      Response response = await dio.get(
        url,
        options: Options(
          contentType: Headers.jsonContentType,
          headers: {
            // 'X-Authorization': 'Bearer $token',
            'Content-Type': 'application/json'
          },
        ),
      );
      if (response.statusCode == 200 && response.data != '') {
        // print(response.data);

        String? getValueByKey(listOfFields, List<String> keys) {
          for (var item in listOfFields) {
            if (keys.contains(item['key'])) {
              return item['value'];
            }
          }
          return null;
        }

        List res = response.data;
        String regionData = getValueByKey(res, ['region']) ?? '';
        String wardData = getValueByKey(res, ['wardName']) ?? '';
        String zoneData = getValueByKey(res, ['zoneName']) ?? '';
        String locationData =
            getValueByKey(res, ['location', 'landmark']) ?? '';

        callback(locationData, zoneData, wardData, regionData);
        EasyLoading.dismiss();
      } else if (response.statusCode == 401) {
        EasyLoading.dismiss();
        await tokenExpired(context, ref);
      } else {
        EasyLoading.dismiss();
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    } catch (e) {
      EasyLoading.dismiss();
      if (e is DioError) {
        if (e.error == 'Session expired. Please login again.') {
          await tokenExpired(context, ref);
        } else {
          dev.log(ErrorMessages.updateTicketServiceError);
        }
      } else {
        dev.log(ErrorMessages.updateTicketServiceTryAgainError);
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    }
  }

  Future<dynamic> getdeviceId(
      context, ref, selectedDeviceType, deviceOrAssetName) async {
    Dio dio = DioClient.dio;

    // Map<String, dynamic> headers = {
    //   'X-Authorization': 'Bearer $token',
    // };
    String type = selectedDeviceType == 'ilm' ||
            selectedDeviceType == 'gw' ||
            selectedDeviceType == 'ilm-4g'
        ? 'devices'
        : 'assets';
    String key = selectedDeviceType == 'ilm' ||
            selectedDeviceType == 'gw' ||
            selectedDeviceType == 'ilm-4g'
        ? 'deviceName'
        : 'assetName';
    if (selectedDeviceType == 'lamp') {
      var decodedData = jsonDecode(deviceOrAssetName);

      String lmpName = decodedData['i'];
      String manufacturer = decodedData['m'];
      String lmpYear = decodedData['y'];
      String dimmable = decodedData['d'];
      String lmpWattage = decodedData['w'];
      String lmpType = decodedData['l'];
      deviceOrAssetName =
          '$manufacturer-$lmpWattage-$lmpType-$dimmable-$lmpYear-$lmpName';
    }
    try {
      Response response = await dio.get(
        '$ticketURL/api/tenant/$type',
        options: Options(),
        queryParameters: {key: deviceOrAssetName},
      );
      if (response.statusCode == 200 && response.data != '') {
        dev.log(response.data["id"].toString());
        Map<String, dynamic> originator = response.data["id"];
        String updatedCustomerId = response.data['ownerId']['id'];
        await ref
            .read(deviceController)
            .updateSelectedCustomerid(updatedCustomerId);
        return originator;
      } else if (response.statusCode == 401) {
        EasyLoading.dismiss();
        await tokenExpired(context, ref);
      } else {
        EasyLoading.dismiss();
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    } catch (e) {
      EasyLoading.dismiss();
      if (e is DioError) {
        if (e.error == 'Session expired. Please login again.') {
          await tokenExpired(context, ref);
        } else {
          dev.log(ErrorMessages.updateTicketServiceError);
        }
      } else {
        dev.log(ErrorMessages.updateTicketServiceTryAgainError);
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    }
  }

  Future<dynamic> createTicketService(context, ref, postData) async {
    dev.log(postData.toString());
    Dio dio = DioClient.dio;
    Map<String, dynamic> headers = {
      'Content-Type': 'application/json',
      // 'X-Authorization': 'Bearer $token',
    };
    try {
      Response response = await dio.post(
        '$ticketURL/api/alarm',
        data: postData,
        options: Options(headers: headers),
      );
      if (response.statusCode == 200 && response.data != '') {
        dev.log(response.data.toString());
        var res = await getCreatedTicketName(
            context,
            ref,
            response.data["customerId"]["id"],
            response.data["id"]["id"],
            postData['details']['t_comment']);
        dev.log(res.toString());
        return res;
      } else if (response.statusCode == 401) {
        EasyLoading.dismiss();
        await tokenExpired(context, ref);
      } else {
        EasyLoading.dismiss();
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    } catch (e) {
      EasyLoading.dismiss();
      if (e is DioError) {
        if (e.error == 'Session expired. Please login again.') {
          await tokenExpired(context, ref);
        } else {
          dev.log(ErrorMessages.updateTicketServiceError);
        }
      } else {
        dev.log(ErrorMessages.updateTicketServiceTryAgainError);
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    }
  }

  Future<dynamic> getCreatedTicketName(context, ref, createdTicketCustomerId,
      createdAlarmId, createdTicketComments) async {
    Dio dio = DioClient.dio;
    Map<String, dynamic> headers = {
      'Content-Type': 'application/json',
      // 'X-Authorization': 'Bearer $token',
    };
    try {
      Response response = await dio.get(
        '$baseURL/api/tickets/serializer/?alarm_id=$createdAlarmId&customer_id=$createdTicketCustomerId',
        options: Options(headers: headers),
      );
      if (response.statusCode == 200 && response.data != '') {
        EasyLoading.dismiss();
        dev.log(response.data.toString());
        var details = jsonDecode(response.data);
        var ticketName = details["details"]["ticket_name"];
        Response commentResponse = await dio.post(
          '$ticketURL/api/alarm/$createdAlarmId/comment',
          data: {
            "alarmId": {"id": createdAlarmId, "entityType": "ALARM"},
            "comment": {"text": createdTicketComments}
          },
          options: Options(headers: headers),
        );
        if (response.statusCode == 200 && response.data != '') {
          var resultData = {
            "status": commentResponse.statusCode,
            "ticketName": ticketName,
          };
          return resultData;
        }
      } else if (response.statusCode == 401) {
        EasyLoading.dismiss();
        await tokenExpired(context, ref);
      } else {
        EasyLoading.dismiss();
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    } catch (e) {
      EasyLoading.dismiss();
      if (e is DioError) {
        if (e.error == 'Session expired. Please login again.') {
          await tokenExpired(context, ref);
        } else {
          dev.log(ErrorMessages.updateTicketServiceError);
        }
      } else {
        dev.log(ErrorMessages.updateTicketServiceTryAgainError);
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    }
  }

  Future<dynamic> getLpDetailsForLampService(ref, context, scannedData) async {
    Dio dio = DioClient.dio;
    // dio.options.headers["token"] = token;

    try {
      Response response = await dio.get(
        '$baseURL/api/entity/search/',
        options: Options(contentType: 'application/json'),
        queryParameters: {'entityName': scannedData},
      );
      // log(response.data).toString();
      if (response.data != "" &&
          !(jsonDecode(response.data).containsKey('status'))) {
        final jsonData = jsonDecode(response.data);
        // callback(
        //     jsonData['deviceDetails'][0]['lightPoint']['landmark'],
        //     jsonData['deviceDetails'][0]['lightPoint']['zoneName'],
        //     jsonData['deviceDetails'][0]['lightPoint']['wardName'],
        //     jsonData['deviceDetails'][0]['lightPoint']['region']);

        return {
          "lmpRegion":
              jsonData['deviceDetails'][0]['lightPoint']['region'] ?? '',
          "lmpWard":
              jsonData['deviceDetails'][0]['lightPoint']['wardName'] ?? '',
          "lmpZone":
              jsonData['deviceDetails'][0]['lightPoint']['zoneName'] ?? '',
          "lmpLocation":
              jsonData['deviceDetails'][0]['lightPoint']['landmark'] ?? '',
        };
      } else if (jsonDecode(response.data).containsKey('status')) {
        var res = jsonDecode(response.data);
        return res['status'];
      } else {
        return 0;
      }
    } catch (e) {
      if (e is DioError) {
        if (e.error == 'Session expired. Please login again.') {
          await tokenExpired(context, ref);
        } else {
          dev.log(ErrorMessages.updateTicketServiceError);
        }
      } else {
        return ErrorMessages.tryAgainError;
      }
    }
  }

  Future<dynamic> getSingleAssetOrDeviceTicketsService(
    ref,
    context,
    String assetOrDevice,
    String assetOrDeviceId,
  ) async {
    Dio dio = DioClient.dio;

    try {
      Response response = await dio.get(
        '$ticketURL/api/alarm/$assetOrDevice/$assetOrDeviceId?status=ACTIVE_UNACK&pageSize=1000&page=0',
        options: Options(
          contentType: Headers.formUrlEncodedContentType,
          headers: {
            // "Authorization": "Bearer $token",
          },
        ),
      );

      if ((response.statusCode == 200 && response.data != '')) {
        dev.log(response.data.toString());
        List<dynamic> data = response.data['data'];

        // Iterate through the list to check for "MCB Trip"
        for (var item in data) {
          if (item['name'] == 'MCB Trip' || item['type'] == 'MCB Trip') {
            return true; // Found "MCB Trip"
          }
        }
      }

      return false; // "MCB Trip" not found

      // String alarmName = response.data['data'].isNotEmpty
      //     ? response.data["data"][0]['name']
      //     : '';
      // String alarmType = response.data['data'].isNotEmpty
      //     ? response.data["data"][0]['type']
      //     : '';
      //return {'alarmName': alarmName, 'alarmType': alarmType};
    } catch (e) {
      EasyLoading.dismiss();
      if (e is DioError) {
        if (e.error == 'Session expired. Please login again.') {
          await tokenExpired(context, ref);
        } else {
          dev.log(ErrorMessages.updateTicketServiceError);
        }
      } else {
        dev.log(ErrorMessages.updateTicketServiceTryAgainError);
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    }
  }

  Future<dynamic> getMcbAlarmForPanelCtrl(
    context,
    ref,
    String assetId,
  ) async {
    Dio dio = DioClient.dio;

    try {
      Response response = await dio.get(
        '$ticketURL/api/alarm/ASSET/$assetId?status=ACTIVE_UNACK&pageSize=1000&page=0',
        options: Options(
          contentType: Headers.formUrlEncodedContentType,
          headers: {
            // "Authorization": "Bearer $token",
          },
        ),
      );

      if ((response.statusCode == 200 && response.data != '')) {
        dev.log(response.data.toString());
        List<dynamic> data = response.data['data'];

        // Iterate through the list to check for "MCB Trip"
        for (var item in data) {
          if (item['name'] == 'MCB Trip' || item['type'] == 'MCB Trip') {
            return true; // Found "MCB Trip"
          }
        }
      }

      return false; // "MCB Trip" not found

      // String alarmName = response.data['data'].isNotEmpty
      //     ? response.data["data"][0]['name']
      //     : '';
      // String alarmType = response.data['data'].isNotEmpty
      //     ? response.data["data"][0]['type']
      //     : '';
      //return {'alarmName': alarmName, 'alarmType': alarmType};
    } catch (e) {
      EasyLoading.dismiss();
      if (e is DioError) {
        if (e.error == 'Session expired. Please login again.') {
          await tokenExpired(context, ref);
        } else {
          dev.log(ErrorMessages.updateTicketServiceError);
        }
      } else {
        dev.log(ErrorMessages.updateTicketServiceTryAgainError);
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
    }
  }
}

List<Map<String, dynamic>> removeDuplicates(List<dynamic> data) {
  Set<String> alarmIds = {};
  List<Map<String, dynamic>> uniqueData = [];

  for (var alarm in data) {
    String alarmId = alarm['id']['id'];
    if (!alarmIds.contains(alarmId)) {
      alarmIds.add(alarmId);
      uniqueData.add(alarm);
    }
  }
  return uniqueData;
}
