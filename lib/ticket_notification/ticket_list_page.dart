import 'dart:developer';
import 'dart:io';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:schnell_luminator/ticket_notification/ticket_controller.dart';
import 'package:schnell_luminator/utils/error_messages.dart';
import '../device_page/device_controller.dart';
import '../location_selection/location_controller.dart';
import '../login_page/login_controller.dart';
import '../utils/asset_folder.dart';
import '../utils/constants.dart';
import '../utils/dialog_box.dart';
import '../utils/utility.dart';
import 'ticket_creation_page.dart';
import 'ticket_details_page.dart';
import 'ticket_model.dart';

const ticketStatusItems = [
  'Ticket Status (ALL)',
  'Open',
  'Assigned',
  'Closed',
  'Visited & Closed',
  'Visited & Not Closed',
  'Verified & Not Closed'
];
// bool doesDeviceTypeExist(deviceType) {
//   for (var key in componenetsChanged.keys) {
//     if (componenetsChanged[key]!
//         .contains(deviceType.toString().toUpperCase())) {
//       return true;
//     }
//   }
//   return false;
// }

bool doesDeviceTypeExist(String deviceType) {
  return componenetsChanged.containsKey(deviceType.toUpperCase());
}

class TicketListPage extends ConsumerWidget {
  const TicketListPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    String selectedCustomer = ref.watch(locationController).selectedCustomer;

    String selectedRegion = ref.watch(locationController).selectedRegion;
    String selectedZone = ref.watch(locationController).selectedZone;
    String selectedWard = ref.watch(locationController).selectedWard;
    String usermail = ref.watch(loginController).userMail;
    String username = usermail.split("@").first.toUpperCase();
    List<Ticket> tickets = ref.watch(ticketController).filteredTickets;
    String ticketStatus = ref.watch(ticketController).ticketStatusValue;
    int totalTicketsCount = ref.watch(ticketController).totalTicketsCount;
    int totalPageCount = ref.watch(ticketController).totalPageCount;
    int scrollCount = ref.watch(ticketController).scrollCount;
    int fetchedTicketCount = ref.watch(ticketController).fetchedTicketCount;
    log('fetched Ticket Count : $fetchedTicketCount');
    String searchText = ref.watch(ticketController).searchText;

    final scrollController = ScrollController();
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (scrollCount + 1 <= totalPageCount) {
          ref
              .read(ticketController)
              .getTicketsBasedOnStatus(context, ref, ticketStatus, true);
        }
      }
    });

    double deviceWidth = MediaQuery.of(context).size.width;
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          leading: Builder(builder: (BuildContext context) {
            return IconButton(
              icon: Icon(
                Icons.menu,
                color: Theme.of(context).cardColor,
              ),
              onPressed: () {
                Scaffold.of(context).openDrawer();
              },
            );
          }),
          backgroundColor: Theme.of(context).primaryColor.withOpacity(0.19),
          elevation: 0.0,
          centerTitle: true,
          titleTextStyle: TextStyle(
            color: Theme.of(context).textTheme.bodySmall!.color,
            fontSize: 18.0,
          ),
          title: Text(
            "Tickets",
            style: TextStyle(color: Theme.of(context).cardColor),
          ),
          actions: <Widget>[
            Row(
              children: [
                IconButton(
                  icon: Icon(Icons.add, color: Theme.of(context).cardColor),
                  onPressed: () {
                    ref
                        .read(ticketController)
                        .updateSelectedFields(context, ref, '', 'reset', false);
                    showTicketCreationDialog(context, ref);
                  },
                ),
              ],
            ),
          ],
        ),
        drawer: customHomeDrawer(
          username,
          usermail,
          context,
          ref,
        ),
        body: SingleChildScrollView(
          child: Column(
            children: [
              const SizedBox(
                height: 5,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: SelectedLocationData(
                    selectedCustomer: selectedCustomer,
                    selectedRegion: selectedRegion,
                    selectedZone: selectedZone,
                    selectedWard: selectedWard),
              ),
              Padding(
                padding: const EdgeInsets.only(
                  left: 18.0,
                ),
                child: Stack(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 45.0,
                      ),
                      child: Row(
                        children: [
                          DropdownButtonHideUnderline(
                            child: DropdownButton2(
                              isDense: true,
                              isExpanded: false,
                              value: ticketStatus,
                              items: ticketStatusItems.map((String items) {
                                return DropdownMenuItem(
                                  alignment: AlignmentDirectional.centerStart,
                                  value: items,
                                  child: Text(
                                    items,
                                    style: TextStyle(
                                        color: Theme.of(context)
                                            .secondaryHeaderColor,
                                        fontSize: 10),
                                  ),
                                );
                              }).toList(),
                              onChanged: (newValue) {
                                Utility.isConnected().then((value) async {
                                  if (value) {
                                    EasyLoading.show(
                                        status: 'loading...',
                                        dismissOnTap: false);
                                    log('Selected status filter = $newValue');
                                    if (context.mounted) {
                                      ref
                                          .read(ticketController)
                                          .getTicketsBasedOnStatus(
                                              context, ref, newValue!);
                                    }
                                  } else {
                                    if (context.mounted) {
                                      await snackBar(
                                          context,
                                          ErrorMessages.offlineErrorTitle,
                                          ErrorMessages.offlineErrorMessage);
                                    }
                                  }
                                });
                              },
                              dropdownStyleData: DropdownStyleData(
                                  padding: const EdgeInsets.only(left: 1),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(3),
                                    color: Theme.of(context).canvasColor,
                                  )),
                              buttonStyleData: ButtonStyleData(
                                height: 35,
                                width: deviceWidth * 0.42,
                                padding:
                                    const EdgeInsets.only(left: 10, right: 10),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(3),
                                  color: Theme.of(context).canvasColor,
                                ),
                                elevation: 1,
                              ),
                            ),
                          ),
                          const SizedBox(width: 10),
                          Container(
                            height: 35,
                            width: deviceWidth * 0.34,
                            padding: const EdgeInsets.only(left: 14, right: 14),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(3),
                              color: Theme.of(context).canvasColor,
                              boxShadow: [
                                BoxShadow(
                                  color: Theme.of(context)
                                      .hoverColor
                                      .withOpacity(0.3),
                                  spreadRadius: 1,
                                  blurRadius: 2,
                                  offset: const Offset(
                                      0, 1), // Adjust the shadow offset
                                ),
                              ],
                            ),
                            child: Center(
                              child: Text(
                                'Device Type (ALL)',
                                style: TextStyle(
                                    color: Theme.of(context).hoverColor,
                                    fontSize: 10),
                              ),
                            ),
                          ),
                          const SizedBox(width: 10),
                        ],
                      ),
                    ),
                    SearchField(ref: ref),
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.only(
                  top: 10,
                  left: 20,
                  right: deviceWidth * 0.07,
                ),
                width: MediaQuery.of(context).size.width,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (searchText != '')
                      Text("${tickets.length} Results found"),
                    const Spacer(),
                    Text(
                      "$fetchedTicketCount / $totalTicketsCount",
                      style: const TextStyle(
                          fontSize: 12, fontWeight: FontWeight.w600),
                    ),
                  ],
                ),
              ),
              Divider(
                color: Theme.of(context).dialogTheme.backgroundColor,
                thickness: 1.5,
              ),
              SizedBox(
                height: MediaQuery.of(context).size.height * 0.65,
                width: MediaQuery.of(context).size.width,
                child: ListView.builder(
                  itemCount: tickets.length,
                  controller: scrollController,
                  itemBuilder: (context, index) {
                    return GestureDetector(
                      onTap: () async {
                        if (tickets[index].ticketStatus != 'Visited & closed' &&
                            tickets[index].ticketStatus != 'Closed' &&
                            tickets[index].ticketStatus !=
                                'Verified & closed') {
                          log('${tickets[index].ticketStatus}');
                          if (doesDeviceTypeExist(
                              tickets[index].deviceType ?? '')) {
                            ref
                                .read(ticketController)
                                .resetFieldValues(tickets[index]);
                            ref
                                .read(ticketController)
                                .updateSelectedTicket(tickets[index]);
                            await ref.read(ticketController).getTicketComments(
                                context,
                                ref,
                                tickets[index].alarmId.toString());
                            if (context.mounted) {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        const TicketDetailsPage()),
                              );
                            }
                          }
                        } else {
                          null;
                        }
                      },
                      child: ListTile(
                        title: TicketDetailsCard(tickets[index]),
                      ),
                    );
                  },
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

class SearchField extends StatefulWidget {
  final WidgetRef ref;

  const SearchField({Key? key, required this.ref}) : super(key: key);

  @override
  State<SearchField> createState() => _SearchFieldState();
}

int toggle = 0;

class _SearchFieldState extends State<SearchField>
    with SingleTickerProviderStateMixin {
  late AnimationController _con;
  late TextEditingController _textEditingController;

  @override
  void initState() {
    super.initState();
    _con = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 375),
    );
    _textEditingController = TextEditingController();
  }

  @override
  void dispose() {
    _con.dispose();
    _textEditingController.dispose();
    super.dispose();
  }

  void resetText() {
    _textEditingController.clear();
  }

  @override
  Widget build(BuildContext context) {
    double deviceWidth = MediaQuery.of(context).size.width;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 375),
      height: 37.0,
      width: (toggle == 0 ? 37.0 : deviceWidth * 0.91),
      curve: Curves.easeOut,
      decoration: BoxDecoration(
        color: Theme.of(context).canvasColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Theme.of(context).hoverColor),
        boxShadow: [
          BoxShadow(
              color: Theme.of(context).secondaryHeaderColor,
              spreadRadius: -10.0,
              blurRadius: 10.0,
              offset: const Offset(0.0, 10.0))
        ],
      ),
      child: Stack(
        children: [
          AnimatedPositioned(
            duration: const Duration(milliseconds: 375),
            left: (toggle == 0) ? 20.0 : 40.0,
            top: 5.0,
            curve: Curves.easeOut,
            child: AnimatedOpacity(
              opacity: (toggle == 0) ? 0.0 : 1.0,
              duration: const Duration(milliseconds: 200),
              child: SizedBox(
                height: 23.0,
                width: deviceWidth * 0.7,
                child: TextFormField(
                  controller: _textEditingController,
                  maxLength: 20,
                  cursorRadius: const Radius.circular(10.0),
                  cursorWidth: 2.0,
                  cursorColor: Theme.of(context).secondaryHeaderColor,
                  decoration: InputDecoration(
                      counterText: '',
                      floatingLabelBehavior: FloatingLabelBehavior.never,
                      labelText: 'Search...',
                      labelStyle: TextStyle(
                          color: Theme.of(context).hoverColor,
                          fontSize: 14.0,
                          fontWeight: FontWeight.w500),
                      alignLabelWithHint: true,
                      border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(20.0),
                          borderSide: BorderSide.none)),
                  onChanged: (value) =>
                      widget.ref.read(ticketController).updateSearchText(value),
                ),
              ),
            ),
          ),
          AnimatedPositioned(
            duration: const Duration(milliseconds: 375),
            right: (toggle == 0) ? 20.0 : 0,
            top: 2.0,
            curve: Curves.easeOut,
            child: AnimatedOpacity(
                opacity: (toggle == 0) ? 0.0 : 1.0,
                duration: const Duration(milliseconds: 200),
                child: SizedBox(
                  height: 23.0,
                  width: deviceWidth * 0.1,
                  child: IconButton(
                    icon: Icon(Icons.close,
                        size: 16, color: Theme.of(context).hoverColor),
                    onPressed: () {
                      resetText();
                      widget.ref.read(ticketController).updateSearchText('');
                      FocusScope.of(context).unfocus();
                      setState(
                        () {
                          if (toggle == 0) {
                            toggle = 1;
                            _con.forward();
                          } else {
                            toggle = 0;
                            _con.reverse();
                          }
                        },
                      );
                    },
                  ),
                )),
          ),
          Material(
            color: Theme.of(context).canvasColor,
            borderRadius: BorderRadius.circular(30.0),
            child: IconButton(
              icon: Icon(Icons.search,
                  size: 18, color: Theme.of(context).cardColor),
              onPressed: () {
                FocusScope.of(context).unfocus();
                setState(
                  () {
                    if (toggle == 0) {
                      toggle = 1;
                      _con.forward();
                    } else {
                      toggle = 0;
                      _con.reverse();
                    }
                  },
                );
              },
            ),
          )
        ],
      ),
    );
  }
}

class TicketDetailsCard extends ConsumerWidget {
  const TicketDetailsCard(this.ticket, {Key? key}) : super(key: key);
  final Ticket ticket;

  Color getTicketSeverityColor(BuildContext context, String value) {
    String severity = value.toString().toUpperCase();
    Color severityColor = Theme.of(context).canvasColor;
    if (severity == 'CRITICAL') {
      severityColor = Theme.of(context).indicatorColor;
    } else if (severity == 'INDETERMINATE') {
      severityColor = Theme.of(context).hintColor;
    } else if (severity == 'WARNING') {
      severityColor = warningColor;
    } else if (severity == 'MAJOR') {
      severityColor = Theme.of(context).dividerColor;
    } else if (severity == 'MINOR') {
      severityColor = Theme.of(context).disabledColor;
    }
    return severityColor;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    double deviceHeight = MediaQuery.of(context).size.height;
    double deviceWidth = MediaQuery.of(context).size.width;
    bool isTicketsOfSpecificDevice =
        ref.watch(ticketController).isTicketsOfSpecificDevice;
    String complaineeType = ticket.complaineeType != null
        ? ticket.complaineeType!.contains("@")
            ? ticket.complaineeType!.split("@").first.toUpperCase()
            : ticket.complaineeType!
        : '';
    log('$isTicketsOfSpecificDevice');
    Color severityColor = getTicketSeverityColor(context, ticket.priority!);
    File imageData =
        imageLocation[ticket.deviceType.toString().toLowerCase()] ??
            Assets.warning;
    return Card(
      color: doesDeviceTypeExist(ticket.deviceType ?? '')
          ? Theme.of(context).canvasColor
          : const Color.fromARGB(255, 201, 199, 199),
      elevation: 2,
      shadowColor: severityColor,
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(3),
            border: Border(
                right: BorderSide(color: severityColor, width: 0.3),
                top: BorderSide(color: severityColor, width: 0.3),
                bottom: BorderSide(color: severityColor, width: 0.3),
                left: BorderSide(color: severityColor, width: 5))),
        child: Padding(
          padding: const EdgeInsets.only(left: 5.0, top: 5.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 4.0),
                    child: SizedBox(
                        width: deviceWidth / 2.50,
                        height: deviceHeight / 35,
                        child: AutoSizeText(
                          '#${ticket.ticketId ?? ''}',
                          style: TextStyle(
                              color: Theme.of(context).cardColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w200),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        )),
                  ),
                  const Spacer(),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Text(
                      ticket.createdTime != null && ticket.createdTime != ''
                          ? StringExtension.displayTimeAgoFromMilliseconds(
                              ticket.createdTime ?? '')
                          : '',
                      style: TextStyle(
                          color: Theme.of(context).hoverColor,
                          fontSize: 10,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                ],
              ),
              if (ticket.deviceId != '' ||
                  ticket.deviceId.toString().toUpperCase() == 'DELETED')
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 3),
                  child: Divider(
                    color: Theme.of(context)
                        .unselectedWidgetColor
                        .withOpacity(0.35),
                    thickness: 0.05,
                    height: 6,
                  ),
                ),
              if (ticket.deviceId != '' ||
                  ticket.deviceId.toString().toUpperCase() == 'DELETED')
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () {
                        if (!isTicketsOfSpecificDevice) {
                          // if (selectedWard != '') {
                          EasyLoading.show(
                            dismissOnTap: false,
                          );
                          ref.read(deviceController).scanDevice(
                              ref, context, 1, "1",
                              isTicket: true, deviceId: ticket.deviceId ?? '');
                          // } else {
                          //   wardRequiredAlert(context, ref,
                          //       'Please select a ward to proceed further..');
                          // }
                        } else {
                          null;
                        }
                      },
                      child: Row(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(left: 5.0),
                            child: Image.file(height: 16, width: 16, imageData),
                          ),
                          const SizedBox(width: 5),
                          SizedBox(
                              width: deviceWidth / 2.2,
                              child: AutoSizeText(
                                ticket.deviceId.toString().toUpperCase() ==
                                        'DELETED'
                                    ? ''
                                    : ticket.deviceId ?? '',
                                style: TextStyle(
                                    color: Theme.of(context).cardColor,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              )),
                        ],
                      ),
                    ),
                    const Spacer(),
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      decoration: BoxDecoration(
                          color: warningColor.withOpacity(0.1),
                          shape: BoxShape.rectangle,
                          borderRadius:
                              const BorderRadius.all(Radius.circular(3))),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 14.0),
                        child: Center(
                          child: Row(
                            children: [
                              const Padding(
                                padding: EdgeInsets.only(right: 5.0),
                                child: Icon(Icons.circle,
                                    size: 10, color: warningColor),
                              ),
                              AutoSizeText(
                                ticket.ticketDetails?['t_type'] !=
                                            '' && //  possible values - manual
                                        ticket.ticketDetails?['t_type'] != null
                                    ? ticket.ticketDetails![
                                        't_type'] //the condition is for: auto tickets doesn't contain the key "t_type"
                                    : 'Auto', //as per dashboard validation, if the key not contain, considered as auto tickets
                                minFontSize: 4,
                                maxFontSize: 24,
                                style: const TextStyle(
                                    fontSize: 11,
                                    fontWeight: FontWeight.bold,
                                    color: warningColor),
                                maxLines: 1,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              if (ticket.problemType != '' && ticket.problemType != null)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 3),
                  child: Divider(
                    color: Theme.of(context)
                        .unselectedWidgetColor
                        .withOpacity(0.35),
                    thickness: 0.05,
                    height: 6,
                  ),
                ),
              Row(
                children: [
                  if (ticket.problemType != '' && ticket.problemType != null)
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(
                                left: 5.0, top: 2, bottom: 2),
                            child: Row(
                              children: [
                                Icon(Icons.warning_amber,
                                    size: 14,
                                    color: Theme.of(context).disabledColor),
                                const SizedBox(
                                  width: 9,
                                ),
                                SizedBox(
                                    // color: Colors.yellow,
                                    width: deviceWidth / 2.35,
                                    child: AutoSizeText(
                                      ticket.problemType ?? '',
                                      style: TextStyle(
                                          color: Theme.of(context).cardColor,
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    )),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  const Spacer(),
                  if (ticket.complaineeType != '' &&
                      ticket.complaineeType != null)
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      decoration: BoxDecoration(
                          color: Theme.of(context).hintColor.withOpacity(0.5),
                          shape: BoxShape.rectangle,
                          borderRadius:
                              const BorderRadius.all(Radius.circular(3))),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 5.0),
                        child: Center(
                          child: Row(
                            children: [
                              const Icon(
                                Icons.person_2_outlined,
                                size: 14,
                              ),
                              AutoSizeText(
                                complaineeType,
                                minFontSize: 1,
                                maxFontSize: 24,
                                style: TextStyle(
                                    fontSize: 11,
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).cardColor),
                                maxLines: 1,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                ],
              ),
              if (((ticket.latitude != '' && ticket.longitude != '') &&
                      (ticket.latitude != null && ticket.longitude != null)) ||
                  ticket.location != '')
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 3),
                  child: Divider(
                    color: Theme.of(context)
                        .unselectedWidgetColor
                        .withOpacity(0.35),
                    thickness: 0.05,
                    height: 6,
                  ),
                ),
              if (((ticket.latitude != '' && ticket.longitude != '') &&
                      (ticket.latitude != null && ticket.longitude != null)) ||
                  ticket.location != '')
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: GestureDetector(
                    onTap: () {
                      if ((ticket.latitude != '' && ticket.longitude != '') &&
                          (ticket.latitude != null &&
                              ticket.longitude != null)) {
                        launchMap(context, ticket);
                      }
                    },
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 3.0),
                          child: ((ticket.latitude != '' &&
                                      ticket.longitude != '') &&
                                  (ticket.latitude != null &&
                                      ticket.longitude != null))
                              ? Image.file(
                                  Assets.gMapPointer,
                                  height: 20,
                                  width: 20,
                                )
                              : Image.file(
                                  Assets.gMapPointer,
                                  height: 14,
                                  width: 14,
                                  color: Theme.of(context).hoverColor,
                                ),
                        ),
                        const SizedBox(
                          width: 5,
                        ),
                        SizedBox(
                          width: deviceWidth * 0.75,
                          child: AutoSizeText(
                            ticket.location ?? '',
                            minFontSize: 8,
                            maxFontSize: 12,
                            maxLines: 2,
                            style: TextStyle(
                                color: Theme.of(context).cardColor,
                                fontSize: 10),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              // if (ticket.ticketDetails?['t_created_by'] != '' &&
              //     ticket.ticketDetails?['t_created_by'] != null)
              //   Padding(
              //     padding: const EdgeInsets.symmetric(vertical: 2),
              //     child: Row(
              //       crossAxisAlignment: CrossAxisAlignment.start,
              //       children: [
              //         Padding(
              //           padding:
              //               const EdgeInsets.only(left: 5.0, top: 2, bottom: 2),
              //           child: Row(
              //             children: [
              //               const Icon(
              //                 Icons.person,
              //                 size: 14,
              //               ),
              //               const SizedBox(
              //                 width: 5,
              //               ),
              //               SizedBox(
              //                 width: deviceWidth * 0.75,
              //                 child: Text(
              //                   maxLines: 3,
              //                   overflow: TextOverflow.ellipsis,
              //                   ticket.ticketDetails?['t_created_by'] ?? '',
              //                   style: TextStyle(
              //                       color: Theme.of(context).cardColor,
              //                       fontSize: 11),
              //                 ),
              //               )
              //             ],
              //           ),
              //         ),
              //       ],
              //     ),
              //   ),
              if (ticket.comment != '' && ticket.comment != null)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 3),
                  child: Divider(
                    color: Theme.of(context)
                        .unselectedWidgetColor
                        .withOpacity(0.35),
                    thickness: 0.05,
                    height: 6,
                  ),
                ),
              if (ticket.comment != '' && ticket.comment != null)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding:
                            const EdgeInsets.only(left: 5.0, top: 2, bottom: 2),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.speaker_notes_outlined,
                              size: 14,
                            ),
                            const SizedBox(
                              width: 5,
                            ),
                            SizedBox(
                              width: deviceWidth * 0.75,
                              child: Text(
                                maxLines: 3,
                                overflow: TextOverflow.ellipsis,
                                ticket.comment ?? '',
                                style: TextStyle(
                                    color: Theme.of(context).cardColor,
                                    fontSize: 11),
                              ),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 3),
                child: Divider(
                  color:
                      Theme.of(context).unselectedWidgetColor.withOpacity(0.35),
                  thickness: 0.05,
                  height: 6,
                ),
              ),
              Container(
                // color: Colors.red,
                margin: const EdgeInsets.only(bottom: 5),
                width: deviceWidth * 0.83,
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Container(
                      width: deviceWidth / 2.5,
                      color: getTicketSeverityColor(context, ticket.priority!)
                          .withOpacity(0.1),
                      padding: const EdgeInsets.all(3.0),
                      child: Center(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(
                              padding: const EdgeInsets.only(
                                  top: 4, bottom: 4, left: 25),
                              decoration: BoxDecoration(
                                color: getTicketSeverityColor(
                                    context, ticket.priority!),
                                shape: BoxShape.circle,
                              ),
                            ),
                            SizedBox(
                              width: deviceWidth * 0.3,
                              child: AutoSizeText(
                                ticket.priority ?? '',
                                style: TextStyle(
                                    color: getTicketSeverityColor(
                                        context, ticket.priority!),
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold),
                                maxLines: 1,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(
                      width: deviceWidth * 0.02,
                    ),
                    Container(
                      width: deviceWidth / 2.7,
                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                      padding: const EdgeInsets.only(
                          top: 3.0, bottom: 3.0, left: 10),
                      child: Center(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(right: 5.0),
                              child: Icon(Icons.circle,
                                  size: 10, color: Theme.of(context).cardColor),
                            ),
                            SizedBox(
                              width: deviceWidth * 0.3,
                              child: AutoSizeText(
                                ticket.ticketStatus?.toUpperCase() ?? '',
                                style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold),
                                minFontSize: 7,
                                maxFontSize: 10,
                                maxLines: 1,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
