import 'dart:developer';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:schnell_luminator/device_page/device_controller.dart';
import 'package:schnell_luminator/home_page/dashboard.dart';
import 'package:schnell_luminator/login_page/login_controller.dart';
import 'package:schnell_luminator/utils/error_messages.dart';
import 'package:searchable_listview/searchable_listview.dart';
import '../ticket_notification/ticket_controller.dart';
import '../utils/dialog_box.dart';
import '../utils/utility.dart';
import 'location_controller.dart';

class CustomerList extends ConsumerWidget {
  const CustomerList({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    List<Customer> customerList = ref.watch(locationController).customerList;
    String selectedCustomer = ref.watch(locationController).selectedCustomer;

    String searchText = '';
    return SafeArea(
        child: WillPopScope(
      onWillPop: () async {
        showExitPopup(context);
        return true;
      },
      child: Scaffold(
        appBar: AppBar(
          leading: selectedCustomer == ''
              ? null
              : IconButton(
                  onPressed: () {
                    Navigator.of(context, rootNavigator: true).pop();
                  },
                  icon: const Icon(Icons.arrow_back),
                  color: Theme.of(context).cardColor),
          automaticallyImplyLeading: false,
          title: Padding(
            padding: const EdgeInsets.only(top: 18.0),
            child: Text(
              'Select a customer',
              style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).cardColor),
            ),
          ),
          backgroundColor: Colors.transparent,
          elevation: 0,
          centerTitle: true,
          actions: [
            IconButton(
              onPressed: () {
                logoutPop(context, ref);
              },
              icon: Icon(
                Icons.logout_outlined,
                size: 27,
                color: Theme.of(context).cardColor,
              ),
            ),
          ],
        ),
        body: Column(
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(15),
                child: SearchableList<Customer>(
                  maxLength: 20,
                  textStyle: TextStyle(
                      fontSize: 20,
                      color: Theme.of(context).secondaryHeaderColor),
                  itemBuilder: (Customer customer) =>
                      CustomerItem(customer: customer, keyContext: context),
                  loadingWidget: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(
                          height: 20,
                        ),
                        Text('Loading region...')
                      ],
                    ),
                  ),
                  errorWidget: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error,
                          color: Theme.of(context).indicatorColor),
                      const SizedBox(
                        height: 20,
                      ),
                      Text(ErrorMessages.regionFetchingError)
                    ],
                  ),
                  // asyncListCallback: () async {
                  //   return Future.value(customerList);
                  // },
                  // asyncListFilter: (q, list) {
                  //   searchText = q;
                  //   return list
                  initialList: customerList,
                  filter: (String q) {
                    return customerList
                        .where((element) => element.name
                            .toLowerCase()
                            .contains(q.toLowerCase()))
                        .toList();
                  },
                  emptyWidget: EmptyView(
                    searchText: searchText,
                  ),
                  onRefresh: () async {},
                  // onItemSelected: (Customer item) {},
                  inputDecoration: InputDecoration(
                    contentPadding: const EdgeInsets.all(16.0),
                    hintText: 'Search customer',
                    counterStyle:
                        TextStyle(color: Theme.of(context).primaryColor),
                    hintStyle: TextStyle(
                        fontSize: 18.0,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: BorderSide.none),
                    filled: true,
                    fillColor: Theme.of(context).primaryColor.withOpacity(0.19),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    ));
  }
}

Future<bool> showExitPopup(context) async {
  var bg = Theme.of(context).dialogTheme.backgroundColor;
  return await showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: bg,
          content: Container(
            height: 100,
            color: bg,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text("Are you sure you want to exit?",
                    style: TextStyle(
                      color: Theme.of(context).secondaryHeaderColor,
                    )),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          if (kDebugMode) {
                            log('yes selected');
                          }
                          exit(0);
                        },
                        style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).indicatorColor),
                        child: Text(
                          "Yes",
                          style:
                              TextStyle(color: Theme.of(context).canvasColor),
                        ),
                      ),
                    ),
                    const SizedBox(width: 15),
                    Expanded(
                        child: ElevatedButton(
                            onPressed: () {
                              if (kDebugMode) {
                                log('no selected');
                              }
                              Navigator.of(context).pop(false);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  Theme.of(context).textTheme.bodySmall!.color,
                            ),
                            child: Text("No",
                                style: TextStyle(
                                  color: Theme.of(context).canvasColor,
                                ))))
                  ],
                )
              ],
            ),
          ),
        );
      });
}

class CustomerItem extends ConsumerWidget {
  final Customer customer;
  final BuildContext keyContext;

  const CustomerItem({
    Key? key,
    required this.customer,
    required this.keyContext,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // final DeviceService _service = DeviceService();

    return GestureDetector(
      child: Container(
        margin: const EdgeInsets.only(left: 3, right: 3, top: 5, bottom: 5),
        decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withOpacity(0.20),
            borderRadius: BorderRadius.circular(18),
            border: Border.all(color: Theme.of(context).unselectedWidgetColor)),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 25.0),
                  child: Icon(Icons.adjust_rounded,
                      color: Theme.of(context).cardColor),
                ),
                Text(
                  customer.name,
                  style: TextStyle(
                      color: Theme.of(context).cardColor,
                      fontWeight: FontWeight.bold),
                )
              ],
            ),
          ),
        ),
      ),
      onTap: () async {
        Utility.isConnected().then((value) async {
          if (value) {
            EasyLoading.show(
              status: 'loading...',
              dismissOnTap: false,
            );
            // await ref
            //     .read(deviceController)
            //     .getCustomerDeviceOrAssetCount(ref, keyContext, customer.id);
            ref.read(ticketController).updateSelectedAssetId(customer.id);
            ref.read(deviceController).updatedSelectedCustomer(customer.name);
            ref
                .read(locationController)
                .updateSelectedLocation(ref, 'customer', '');
            await ref
                .read(loginController)
                .paramsForTheWebViewCustomer(ref, customer.id, customer.name);

            if (context.mounted) {
              ref
                  .read(locationController)
                  .getRegionDetails(context, ref, customer.id, customer.name)
                  .then((val) async {
                log('********'.toString());
                log(val.toString());
                log('********'.toString());

                try {
                  if (val == 1) {
                    await EasyLoading.dismiss();
                    ref.read(bottomMenuStateProvider.state).state = 0;
                    if (keyContext.mounted) {
                      await Navigator.push(
                        keyContext,
                        MaterialPageRoute(
                          builder: (context) => HomePage(),
                        ),
                      );
                    }
                  }
                } catch (e) {
                  await EasyLoading.dismiss();
                  log('Navigation error: $e');
                }
              });
            }
          } else {
            if (context.mounted) {
              await snackBar(context, ErrorMessages.offlineErrorTitle,
                  ErrorMessages.offlineErrorMessage);
            }
          }
        });
      },
    );
  }
}

class EmptyView extends StatelessWidget {
  final String searchText;

  const EmptyView({required this.searchText, Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.error, color: Theme.of(context).indicatorColor),
        Text(ErrorMessages.customerNotFoundError),
      ],
    );
  }
}

class Customer {
  String id;
  String name;

  Customer({
    required this.id,
    required this.name,
  });
}
