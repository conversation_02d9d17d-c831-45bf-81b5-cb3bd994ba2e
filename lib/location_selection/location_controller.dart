import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:latlong2/latlong.dart';
import 'package:location/location.dart';
import 'package:schnell_luminator/device_page/device_controller.dart';
import 'package:schnell_luminator/home_page/dashboard.dart';
import 'package:schnell_luminator/location_selection/region_list.dart';
import 'package:schnell_luminator/location_selection/zone_list.dart';
import 'package:schnell_luminator/utils/constants.dart';
import 'package:schnell_luminator/utils/error_messages.dart';
import '../ticket_notification/ticket_controller.dart';
import '../utils/dialog_box.dart';
import 'customer_list.dart';
import 'location_service.dart';
import 'ward_list.dart';

final locationController =
    ChangeNotifierProvider<SplashProvider>((ref) => SplashProvider());

class SplashProvider extends ChangeNotifier {
  final LocationService _service = LocationService();
  List<Customer> _customerList = [];
  List<Region> _regionList = [];
  List<Zone> _zoneList = [];
  List<Ward> _wardList = [];
  String _selectedRegion = '';
  String _selectedCustomer = '';
  String _selectedZone = '';
  String _selectedWard = '';
  String _selectedWardId = '';
  bool _loading = false;
  bool _islength = false;
  LatLng? _userLocation;
  // bool isLoading = true;

  void loadingChange() {
    _loading = !_loading;
    notifyListeners();
  }

  void emptyCustomer() {
    _selectedCustomer = '';
  }

  Future<void> updateCustomer(data) async {
    log('$data');
    _customerList = [];
    await data["customers"].forEach((d) => _customerList.add(Customer(
          id: d['id'],
          name: d['name'],
        )));
    _customerList.sort((a, b) => a.name.compareTo(b.name));
    notifyListeners();
  }

  Future<void> updateRegion(data) async {
    _regionList = [];
    await data["regions"].forEach((d) => _regionList.add(Region(
        id: d['id'],
        customerId: d['customerId'],
        name: d['name'],
        type: 'region',
        poleSchema: d['poleSchema'])));
    _regionList.sort((a, b) => a.name.compareTo(b.name));
    notifyListeners();
  }

  void updateZone(data) async {
    _zoneList = [];
    await data["zones"].forEach((d) =>
        _zoneList.add(Zone(id: d['id'], name: d['name'], type: d['type'])));
    _zoneList.sort((a, b) => a.name.compareTo(b.name));
    notifyListeners();
  }

  Future<void> updateWard(data) async {
    _wardList = [];
    await data["wards"].forEach((d) =>
        _wardList.add(Ward(id: d['id'], name: d['name'], type: d['type'])));
    _wardList.sort((a, b) => a.name.compareTo(b.name));
    notifyListeners();
  }

  void zoneListLength() {
    if (zoneList.length == 1) {
      _islength = true;
    }
    notifyListeners();
  }

  Future<int?> getJwtExpiry(String? token) async {
    try {
      // Split the token into parts
      final parts = token!.split('.');
      if (parts.length != 3) return null;

      // Decode the payload (middle part)
      final payload = parts[1];
      final normalized = base64Url.normalize(payload);
      final decoded = utf8.decode(base64Url.decode(normalized));
      final Map<String, dynamic> data = json.decode(decoded);

      // Get the expiry (exp) claim, which is in seconds since epoch
      final exp = data['exp'] as int?;
      if (exp == null) return null;

      // Convert from seconds to milliseconds
      return exp * 1000;
    } catch (e) {
      log('Error decoding JWT: $e');
      return null;
    }
  }

  Future<void> getCustomerDetails(ref, context) async {
    var res = await _service.getCustomerService(ref, context);
    if (res == 1) {
      Navigator.of(context)
          .pushNamedAndRemoveUntil(customerRoute, (route) => false);
    } else if (res == 401) {
      loadingChange();
      await tokenExpired(context, ref);
    } else if (res == 404 || res == 400) {
      EasyLoading.dismiss();
      showSnackBar(ref, context, ErrorMessages.invalidQRError);
    } else if (res.toString() == "Server Timeout. Please try Again!") {
      EasyLoading.dismiss();
      showSnackBar(ref, context, ErrorMessages.systemNotResponsiveError);
    } else {
      EasyLoading.dismiss();
      showSnackBar(ref, context, ErrorMessages.tryAgainError);
    }
  }

  // Future<int> getRegionDetails(ref, context, customerId) async {
  //   var res = await _service.getRegionService(ref, context);
  //   if (res == 1) {
  //   } else if (res == 401) {
  //     loadingChange();
  //     await tokenExpired(context, ref);
  //   } else if (res == 404 || res == 400) {
  //     EasyLoading.dismiss();
  //     showSnackBar(ref, context, ErrorMessages.invalidQRError);
  //   } else if (res.toString() == "Server Timeout. Please try Again!") {
  //     EasyLoading.dismiss();
  //     showSnackBar(ref, context, ErrorMessages.systemNotResponsiveError);
  //   } else {
  //     EasyLoading.dismiss();
  //     showSnackBar(ref, context, ErrorMessages.tryAgainError);
  //   }
  // }

  Future<int> getRegionDetails(
      BuildContext context, WidgetRef ref, customerId, customerName) async {
    ref.read(deviceController).updateSelectedCustomerid(customerId);
    await ref.read(ticketController).isTicketsOfSpecificDeviceOrAsset(false);
    if (context.mounted) {
      await ref
          .read(ticketController)
          .getTickets(context, ref, selectedRegion: _selectedRegion);
    }
    var res =
        await _service.getRegionService(context, ref, customerId, updateRegion);
    if (res == 1) {
      _selectedCustomer = customerName;
      return Future.value(1);
    } else if (res == 401) {
      if (context.mounted) {
        await tokenExpired(context, ref);
      }
      return Future.value(0);
    } else if (res == 404 || res == 400) {
      EasyLoading.dismiss();

      if (context.mounted) {
        showSnackBar(ref, context, ErrorMessages.invalidQRError);
      }
      return Future.value(0);
    } else if (res.toString() == ErrorMessages.serverTimeOutError) {
      EasyLoading.dismiss();

      if (context.mounted) {
        showSnackBar(ref, context, ErrorMessages.systemNotResponsiveError);
      }
      return Future.value(0);
    } else {
      EasyLoading.dismiss();
      if (context.mounted) {
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
      return Future.value(0);
    }
  }

  Future<bool> getZoneDetails(BuildContext context, WidgetRef ref, regionId,
      regionName, customerId, poleSchema) async {
    await ref
        .read(deviceController)
        .updatedSelectedRegion(regionName, poleSchema);

    var res = await _service.getZoneService(context, ref, regionId, updateZone);
    if (res == 1) {
      _selectedRegion = regionName;
      return Future.value(true);
    } else if (res == 401) {
      if (context.mounted) {
        await tokenExpired(context, ref);
      }
      return Future.value(false);
    } else if (res == 404 || res == 400) {
      EasyLoading.dismiss();

      if (context.mounted) {
        showSnackBar(ref, context, ErrorMessages.invalidQRError);
      }
      return Future.value(false);
    } else if (res.toString() == ErrorMessages.serverTimeOutError) {
      EasyLoading.dismiss();

      if (context.mounted) {
        showSnackBar(ref, context, ErrorMessages.systemNotResponsiveError);
      }
      return Future.value(false);
    } else {
      EasyLoading.dismiss();
      if (context.mounted) {
        showSnackBar(ref, context, ErrorMessages.tryAgainError);
      }
      return Future.value(false);
    }
  }

  Future<bool> getWardDetails(
      BuildContext context, WidgetRef ref, data, val) async {
    await ref.read(deviceController).updatedSelectedZone(val);
    if (context.mounted) {
      var res = await _service.getWardService(context, ref, data, updateWard);
      if (res == 1) {
        _selectedZone = val;
        return Future.value(true);
      } else if (res == 401) {
        if (context.mounted) {
          await tokenExpired(context, ref);
        }
        return Future.value(false);
      } else if (res == 404 || res == 400) {
        EasyLoading.dismiss();
        if (context.mounted) {
          showSnackBar(ref, context, ErrorMessages.invalidQRError);
        }
        return Future.value(false);
      } else if (res.toString() == ErrorMessages.serverTimeOutError) {
        EasyLoading.dismiss();
        if (context.mounted) {
          showSnackBar(ref, context, ErrorMessages.systemNotResponsiveError);
        }
        return Future.value(false);
      } else {
        EasyLoading.dismiss();
        if (context.mounted) {
          showSnackBar(ref, context, ErrorMessages.tryAgainError);
        }
        return Future.value(false);
      }
    }
    return Future.value(false);
  }

  Future<void> updateWardId(
      BuildContext context, WidgetRef ref, val, id) async {
    _selectedWard = val;
    _selectedWardId = id;
    ref.read(deviceController).selectedWardUpdate(val, id);
    EasyLoading.dismiss();
    ref.read(bottomMenuStateProvider.state).state = 0;
    notifyListeners();
  }

  void updateSelectedLocation(
      WidgetRef ref, String locationType, String value) {
    switch (locationType) {
      case 'customer':
        _selectedRegion = value;
        _selectedZone = value;
        _selectedWard = value;
        ref.read(deviceController).updatedSelectedRegion('', '');
        ref.read(deviceController).updatedSelectedZone('');
        ref.read(deviceController).selectedWardUpdate('', '');
        _wardList.clear();
        break;
      case 'region':
        _selectedZone = value;
        _selectedWard = value;
        ref.read(deviceController).updatedSelectedZone('');
        ref.read(deviceController).selectedWardUpdate('', '');
        _wardList.clear();
        break;
      default:
        _selectedWard = value;
        ref.read(deviceController).selectedWardUpdate('', '');
    }
    notifyListeners();
  }

  Future<void> fetchCurrentLocation() async {
    try {
      final location = await determinePosition();

      _userLocation = location;
    } catch (e) {
      _userLocation = const LatLng(11.0678, 76.9465);

      log('Error fetching location: $e');
    }
  }

  Future<LatLng> determinePosition() async {
    bool serviceEnabled;
    PermissionStatus permissionGranted;
    serviceEnabled = await Location().serviceEnabled();
    if (!serviceEnabled) {
      serviceEnabled = await Location().requestService();
      if (!serviceEnabled) {
        return Future.error('Location services are disabled.');
      }
    }

    permissionGranted = await Location().hasPermission();
    if (permissionGranted == PermissionStatus.denied) {
      permissionGranted = await Location().requestPermission();
      if (permissionGranted != PermissionStatus.granted) {
        return Future.error('Location permissions are denied');
      }
    }

    if (permissionGranted == PermissionStatus.deniedForever) {
      return Future.error(
          'Location permissions are permanently denied, we cannot request permissions.');
    }

    final position = await Location().getLocation();
    return LatLng(position.latitude!, position.longitude!);
  }

  String get selectedRegion => _selectedRegion;
  String get selectedCustomer => _selectedCustomer;
  String get selectedZone => _selectedZone;
  String get selectedWard => _selectedWard;
  String get selectedWardId => _selectedWardId;
  bool get loading => _loading;
  LatLng? get userLocation => _userLocation;
  bool get islength => _islength;
  List<Region> get regionList => _regionList;
  List<Customer> get customerList => _customerList;
  List<Zone> get zoneList => _zoneList;
  List<Ward> get wardList => _wardList;
}
