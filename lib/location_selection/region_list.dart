import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:schnell_luminator/utils/error_messages.dart';
import 'package:searchable_listview/searchable_listview.dart';
import '../login_page/login_controller.dart';
import '../ticket_notification/ticket_controller.dart';
import '../utils/dialog_box.dart';
import '../utils/utility.dart';
import 'location_controller.dart';

void showRegionDialog(
  ref,
  context,
) {
  showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
            shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(24.0))),
            insetPadding: const EdgeInsets.all(8.0),
            backgroundColor: Theme.of(context).canvasColor,
            content: SizedBox(
                height: MediaQuery.of(context).size.height * 0.65,
                width: MediaQuery.of(context).size.width * 0.70,
                child: const RegionList()));
      });
}

class RegionList extends ConsumerWidget {
  const RegionList({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    List<Region> regionList = ref.watch(locationController).regionList;

    return SearchableList<Region>(
      maxLength: 20,
      textStyle: const TextStyle(fontSize: 25),
      itemBuilder: (Region region) => RegionItem(region: region),
      loadingWidget: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(
              height: 10,
            ),
            Text('Loading region...')
          ],
        ),
      ),
      errorWidget: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error,
            color: Theme.of(context).indicatorColor,
          ),
          const SizedBox(
            height: 10,
          ),
          Text(ErrorMessages.regionFetchingError)
        ],
      ),
      // asyncListCallback: () async {
      //   return regionList;
      // },
      // asyncListFilter: (q, list) {
      //   return list
      initialList: regionList,

      filter: (String q) {
        return regionList
            .where((element) =>
                element.name.toLowerCase().contains(q.toLowerCase()))
            .toList();
      },
      emptyWidget: const EmptyView(),
      onRefresh: () async {},
      // onItemSelected: (Region item) {},
      inputDecoration: InputDecoration(
        contentPadding: const EdgeInsets.all(2.0),
        hintText: '     Search region',
        counterStyle: TextStyle(color: Theme.of(context).primaryColor),
        hintStyle: Theme.of(context).textTheme.titleLarge!.copyWith(
            fontSize: 15.0,
            color: Theme.of(context).primaryColor,
            fontWeight: FontWeight.bold),
        border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: BorderSide.none),
        filled: true,
        fillColor: Theme.of(context).primaryColor.withOpacity(0.19),
      ),
    );
  }
}

class RegionItem extends ConsumerWidget {
  final Region region;
  const RegionItem({
    Key? key,
    required this.region,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GestureDetector(
      child: Container(
        padding: const EdgeInsets.all(8.0),
        margin: const EdgeInsetsDirectional.only(bottom: 8),
        decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withOpacity(0.20),
            border: Border.all(color: Theme.of(context).unselectedWidgetColor),
            borderRadius: BorderRadius.circular(8)),
        child: Center(
          child: Text(
            region.name,
            style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).cardColor,
                fontWeight: FontWeight.bold),
          ),
        ),
      ),
      onTap: () async {
        Utility.isConnected().then((value) async {
          if (value) {
            EasyLoading.show(status: 'loading...', dismissOnTap: false);
            // await ref
            //     .read(deviceController)
            //     .getDeviceOrAssetsWardCount(ref, context, region.id);
            ref.read(ticketController).updateSelectedAssetId(region.id);
            await ref
                .read(ticketController)
                .isTicketsOfSpecificDeviceOrAsset(false);
            if (context.mounted) {
              await ref
                  .read(ticketController)
                  .getTickets(context, ref, selectedRegion: region.name);
            }
            await ref.read(loginController).paramsForTheWebViewWard(
                ref, region.id, region.name, region.name);
            if (context.mounted) {
              await ref
                  .read(locationController)
                  .getZoneDetails(context, ref, region.id, region.name,
                      region.customerId, region.poleSchema)
                  .then((value) {
                if (value == true) {
                  EasyLoading.dismiss();
                  ref
                      .read(locationController)
                      .updateSelectedLocation(ref, 'region', '');
                  if (context.mounted) {
                    Navigator.of(context, rootNavigator: true).pop();
                  }
                }
              });
            }
          } else {
            if (context.mounted) {
              await snackBar(context, ErrorMessages.offlineErrorTitle,
                  ErrorMessages.offlineErrorMessage);
            }
          }
        });
      },
    );
  }
}

class EmptyView extends StatelessWidget {
  const EmptyView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.error, color: Theme.of(context).indicatorColor),
        Text(ErrorMessages.regionNotFoundError),
      ],
    );
  }
}

class Region {
  String id;
  String customerId;
  String name;
  String type;
  String poleSchema;

  Region({
    required this.id,
    required this.customerId,
    required this.name,
    required this.type,
    required this.poleSchema,
  });
}
