import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:schnell_luminator/location_selection/location_controller.dart';
import 'package:schnell_luminator/utils/dialog_box.dart';
import 'package:schnell_luminator/utils/error_messages.dart';
import 'package:searchable_listview/searchable_listview.dart';
import '../login_page/login_controller.dart';
import '../ticket_notification/ticket_controller.dart';
import '../utils/utility.dart' show Utility;

void showWardSelectDialog(ref, context) {
  showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
            shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(24.0))),
            insetPadding: const EdgeInsets.all(8.0),
            backgroundColor: Theme.of(context).canvasColor,
            content: SizedBox(
                height: MediaQuery.of(context).size.height * 0.65,
                width: MediaQuery.of(context).size.width * 0.70,
                child: const WardList()));
      });
}

class WardList extends ConsumerWidget {
  const WardList({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    List<Ward> wardList = ref.watch(locationController).wardList;

    return SafeArea(
      child: Scaffold(
        body: SearchableList<Ward>(
          maxLength: 20,
          textStyle: const TextStyle(fontSize: 25),
          itemBuilder: (Ward zone) => WardItem(ward: zone),
          loadingWidget: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(
                  height: 20,
                ),
                Text('Loading ward...')
              ],
            ),
          ),
          errorWidget: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error,
                color: Theme.of(context).indicatorColor,
              ),
              const SizedBox(
                height: 20,
              ),
              Text(ErrorMessages.wardFetchingError)
            ],
          ),
          //
          initialList: wardList,

          filter: (String q) {
            return wardList
                .where((element) =>
                    element.name.toLowerCase().contains(q.toLowerCase()))
                .toList();
          },
          emptyWidget: const EmptyView(),
          onRefresh: () async {},
          // onItemSelected: (Ward item) {},
          inputDecoration: InputDecoration(
            contentPadding: const EdgeInsets.all(2.0),
            counterStyle: TextStyle(color: Theme.of(context).primaryColor),
            hintText: '     Search ward',
            hintStyle: Theme.of(context).textTheme.titleLarge!.copyWith(
                fontSize: 15.0,
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.bold),
            border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
                borderSide: BorderSide.none),
            filled: true,
            fillColor: Theme.of(context).primaryColor.withOpacity(0.19),
          ),
        ),
      ),
    );
  }
}

class WardItem extends ConsumerWidget {
  final Ward ward;

  const WardItem({
    Key? key,
    required this.ward,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    String selectedRegion = ref.watch(locationController).selectedRegion;
    String selectedZone = ref.watch(locationController).selectedZone;

    return GestureDetector(
      child: Container(
        padding: const EdgeInsets.all(8.0),
        margin: const EdgeInsetsDirectional.only(bottom: 8),
        decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withOpacity(0.20),
            border: Border.all(color: Theme.of(context).unselectedWidgetColor),
            borderRadius: BorderRadius.circular(8)),
        child: Center(
          child: Text(
            ward.name,
            style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).cardColor,
                fontWeight: FontWeight.bold),
          ),
        ),
      ),
      onTap: () async {
        Utility.isConnected().then((value) async {
          if (value) {
            EasyLoading.show(status: 'loading...', dismissOnTap: false);
            // await ref
            //     .read(deviceController)
            //     .getDeviceOrAssetsWardCount(ref, context, ward.id);
            ref.read(ticketController).updateSelectedAssetId(ward.id);
            await ref
                .read(ticketController)
                .isTicketsOfSpecificDeviceOrAsset(false);

            if (context.mounted) {
              await ref
                  .read(ticketController)
                  .getTickets(context, ref, selectedRegion: selectedRegion);
            }
            await ref.read(loginController).paramsForTheWebViewWard(
                ref,
                ward.id,
                ward.name,
                '$selectedRegion > $selectedZone > ${ward.name}');
            EasyLoading.dismiss();
            if (context.mounted) {
              ref
                  .read(locationController)
                  .updateWardId(context, ref, ward.name, ward.id);
            }
            if (context.mounted) {
              Navigator.of(context, rootNavigator: true).pop();
            } // await saveLocation(selectedWard, 'ward');
          } else {
            if (context.mounted) {
              await snackBar(context, ErrorMessages.offlineErrorTitle,
                  ErrorMessages.offlineErrorMessage);
            }
          }
        });
      },
    );
  }
}

class EmptyView extends StatelessWidget {
  const EmptyView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.error, color: Theme.of(context).indicatorColor),
        Text(ErrorMessages.wardNotFoundError),
      ],
    );
  }
}

class Ward {
  String id;
  String name;
  String type;

  Ward({
    required this.id,
    required this.name,
    required this.type,
  });
}
