import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:schnell_luminator/attendence_tracking.dart/ppe_integration.dart';
import 'package:schnell_luminator/attendence_tracking.dart/user_tracking_controller.dart';
import 'package:schnell_luminator/location_selection/location_controller.dart';
import 'package:schnell_luminator/utils/error_messages.dart';
import 'package:schnell_luminator/utils/session.dart';
import 'package:schnell_luminator/utils/utility.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:url_launcher/url_launcher.dart';
import '../attendence_tracking.dart/worker_thread.dart';
import '../device_page/device_controller.dart';
import '../home_page/dashboard.dart';
import '../utils/constants.dart';
import '../utils/dialog_box.dart';
import 'login_model.dart';
import 'login_service.dart';

final loginController =
    ChangeNotifierProvider<LoginProvider>((ref) => LoginProvider());

class LoginProvider extends ChangeNotifier {
  final LoginService _service = LoginService();
  bool _passwordVisible = true;
  String _userMail = '';
  bool _loginLoader = false;
  // bool _isUserLoggedIn = true;
  String _oldAppAlertMsg = '';
  final UserModel _userLogin = UserModel(username: '', password: '');
  String accessToken = '';
  dynamic urlEncodedData;
  String _accessTokenForWebView = '';
  String _stateParamsForWeView = '';

  passwordVisibleChange() {
    _passwordVisible = !_passwordVisible;
    notifyListeners();
  }

  textFieldUpdate(val, isUserName) {
    if (isUserName) {
      _userLogin.username = val;
    } else {
      _userLogin.password = val;
    }
    notifyListeners();
  }

  void loginLoadChange() {
    _loginLoader = !_loginLoader;
    notifyListeners();
  }

  void logoutLoadfalse() {
    _loginLoader = false;
    notifyListeners();
  }

  // void isTheUserLoggedIn(value) {
  //   _isUserLoggedIn = value;
  //   log('_isUserLoggedIn $_isUserLoggedIn');
  //   notifyListeners();
  // }

  Future<void> validateLogin(context, WidgetRef ref, username, password) async {
    var result = await _service.validateUser(
      context,
      UserModel(username: username, password: password),
    );
    final prefs = await SharedPreferences.getInstance();
    bool? isLocationTrackingRequired =
        prefs.getBool('isLocationTrackingRequired');
    bool? isPPERequired = prefs.getBool('isPPERequired');
    int? locationTrackingInterval =
        prefs.getInt('locationTrackingInterval'); //in millisecs

    if (result == 1) {
      _userMail = username.toString();

      //for auto logout after 24 hrs
      prefs.setInt('loginTimestamp', DateTime.now().millisecondsSinceEpoch);

      // only for the users who have rights to track the location
      if (isLocationTrackingRequired!) {
        await requestNotificationPermissionUntilGranted(context);

        if (await Permission.notification.isGranted) {
          await ref
              .read(userTrackingController)
              .initForegroundTask(locationTrackingInterval!);
          await initializeService();
        } else {
          log("Cannot proceed without notification permission");
          await requestNotificationPermissionUntilGranted(context);
        }
      }

      // only for the users who have rights to access safety kit
      if (isPPERequired!) {
        await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const PpeTakePictureScreen(),
          ),
        );
      } else {
        await ref.read(locationController).getCustomerDetails(ref, context);
      }
      await ref.read(locationController).getCustomerDetails(ref, context);
    } else if (result is Map && result["status"] == 400) {
      loginLoadChange();
      showSnackBar(ref, context, result['message'].toString());
      if (ModalRoute.of(context)!.settings.name != loginRoute) {
        Navigator.of(context)
            .pushNamedAndRemoveUntil(loginRoute, (route) => false);
      }
    } else if (result is Map &&
        result.containsKey('status') &&
        result['status'] != 200 &&
        result['status'] != 500) {
      if (result['status'] == 1001) {
        _oldAppAlertMsg = result['message'].toString();
        loginLoadChange();
        Navigator.of(context)
            .pushNamedAndRemoveUntil(updateRoute, (route) => false);
      } else {
        loginLoadChange();
        showSnackBar(ref, context, result['message'].toString());
        if (ModalRoute.of(context)!.settings.name != loginRoute) {
          Navigator.of(context)
              .pushNamedAndRemoveUntil(loginRoute, (route) => false);
        }
      }
    } else if (result == '500' ||
        (result is Map &&
            (result["status"] == 500 || result["status"] == 503))) {
      loginLoadChange();
      showSnackBar(ref, context, ErrorMessages.systemNotResponsiveError);
      if (ModalRoute.of(context)!.settings.name != loginRoute) {
        Navigator.of(context)
            .pushNamedAndRemoveUntil(loginRoute, (route) => false);
      }
    } else {
      loginLoadChange();
      showSnackBar(ref, context, ErrorMessages.tryAgainError);
      if (ModalRoute.of(context)!.settings.name != loginRoute) {
        Navigator.of(context)
            .pushNamedAndRemoveUntil(loginRoute, (route) => false);
      }
    }
  }

  Future<void> logout(context, WidgetRef ref) async {
    EasyLoading.show(
      status: '',
      dismissOnTap: false,
    );
    var result = await _service.logoutService(
      context,
    );
    EasyLoading.dismiss();
    if (result == 1 || result == 401) {
      ref.read(deviceController).emptyLocation();
      ref.read(locationController).emptyCustomer();
      removeUser();
      // isTheUserLoggedIn(false);
      ref.read(loginController).logoutLoadfalse();
      ref.read(loginController).textFieldUpdate('', true);
      ref.read(loginController).textFieldUpdate('', false);
      final prefs = await SharedPreferences.getInstance();
      bool? isLocationTrackingRequired =
          prefs.getBool('isLocationTrackingRequired');
      if (isLocationTrackingRequired!) {
        stopLocationTracking();
      }

      Navigator.of(context).pushNamedAndRemoveUntil(
        loginRoute,
        (Route<dynamic> route) => false,
      );
    } else if (result is Map &&
        result.containsKey('status') &&
        result['status'] != 200 &&
        result['status'] != 500) {
      EasyLoading.dismiss();
      showSnackBar(ref, context, result['message'].toString());
    } else if (result == '500' ||
        (result is Map &&
            (result["status"] == 500 || result["status"] == 503))) {
      EasyLoading.dismiss();
      showSnackBar(ref, context, ErrorMessages.systemNotResponsiveError);
    } else {
      EasyLoading.dismiss();
      showSnackBar(ref, context, ErrorMessages.tryAgainError);
    }
  }

  Future<void> paramsForTheWebViewCustomer(
      WidgetRef ref, selectedCustomerId, selectedCustomerName) async {
    final prefs = await SharedPreferences.getInstance();
    accessToken = prefs.getString('token')!;
    log(' customerId: $selectedCustomerId customerName: $selectedCustomerName');
    final data = [
      {
        "id": "default",
        "params": {
          "selectedCustomer": {
            "entityId": {"id": selectedCustomerId, "entityType": "CUSTOMER"},
            "entityName": selectedCustomerName
          },
          "selectedEntity": {
            "entityId": {"id": selectedCustomerId, "entityType": "CUSTOMER"},
            "entityName": selectedCustomerName,
            "entityLabel": selectedCustomerName
          },
          "targetEntityParamName": "selectedEntity"
        }
      }
    ];
    final jsonData = json.encode(data);
    final base64Data = base64.encode(utf8.encode(jsonData));
    urlEncodedData = Uri.encodeComponent(base64Data);
    log('Url for the webview : $ticketURL/dashboard/$webviewDashboardId?accessToken=$accessToken&state=$urlEncodedData');
    // return {'accessToken': accessToken, 'stateParam': urlEncodedData};
    _accessTokenForWebView = accessToken;
    _stateParamsForWeView = urlEncodedData;
  }

  Future<void> reloadWebView(
      WidgetRef ref, accessTokenForWebView, stateParamsForWeView) async {
    var webViewController = ref.watch(inappController).webViewController;
    if (webViewController != null) {
      await webViewController.loadUrl(
        urlRequest: URLRequest(
            url: WebUri(
                '$ticketURL/dashboard/$webviewDashboardId?accessToken=$accessTokenForWebView&state=$stateParamsForWeView')),
      );
    }
  }

  Future<void> paramsForTheWebViewWard(WidgetRef ref, regionZoneOrWardId,
      regionZoneOrWardName, regionZoneOrWardLabel) async {
    final prefs = await SharedPreferences.getInstance();
    accessToken = prefs.getString('token')!;
    String selectedCustomerId = ref.watch(deviceController).selectedCustomerId;
    String selectedCustomerName = ref.watch(deviceController).selectedCustomer;
    log('regionLabel: $regionZoneOrWardLabel  regionId: $regionZoneOrWardId  regionName: $regionZoneOrWardName  customerId: $selectedCustomerId customerName: $selectedCustomerName');
    final data = [
      {
        "id": "default",
        "params": {
          "selectedCustomer": {
            "entityId": {"id": selectedCustomerId, "entityType": "CUSTOMER"},
            "entityName": selectedCustomerName
          },
          "selectedEntity": {
            "entityId": {"id": regionZoneOrWardId, "entityType": "ASSET"},
            "entityName": regionZoneOrWardName,
            "entityLabel": regionZoneOrWardLabel
          },
          "targetEntityParamName": "selectedEntity"
        }
      }
    ];
    final jsonData = json.encode(data);
    final base64Data = base64.encode(utf8.encode(jsonData));
    urlEncodedData = Uri.encodeComponent(base64Data);
    log('Url for the webview : $ticketURL/dashboard/$webviewDashboardId?accessToken=$accessToken&state=$urlEncodedData');
    _accessTokenForWebView = accessToken;
    _stateParamsForWeView = urlEncodedData;
    reloadWebView(ref, _accessTokenForWebView, _stateParamsForWeView);
  }

  Future<void> launchUrl() async {
    try {
      launch("market://details?id=" +
          'com.schnell.schnell_luminator.schnell_luminator');
    } on PlatformException catch (e) {
      launch("https://play.google.com/store/apps/details?id=" +
          'com.schnell.schnell_luminator.schnell_luminator');
    } finally {
      launch("https://play.google.com/store/apps/details?id=" +
          'com.schnell.schnell_luminator.schnell_luminator');
    }
  }

  void setUserMail(String value) {
    _userMail = value;
    notifyListeners();
  }

  bool get passwordVisible => _passwordVisible;
  bool get loginLoader => _loginLoader;
  // bool get isUserLoggedIn => _isUserLoggedIn;
  String get userMail => _userMail;
  String get oldAppAlertMsg => _oldAppAlertMsg;
  String get stateParamsForWeView => _stateParamsForWeView;
  String get accessTokenForWebView => _accessTokenForWebView;
  UserModel get userLogin => _userLogin;
}
