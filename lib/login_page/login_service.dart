import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:geolocator/geolocator.dart';
import 'package:schnell_luminator/utils/dio_client.dart';
import 'package:schnell_luminator/utils/error_messages.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../utils/constants.dart';
import '../utils/session.dart';
import 'login_model.dart';

class LoginService {
  Future<dynamic> validateUser(context, UserModel user) async {
    final position = await Geolocator.getCurrentPosition(
      locationSettings: AndroidSettings(accuracy: LocationAccuracy.high),
    );

    final prefs = await SharedPreferences.getInstance();
    Map<String, dynamic> data = {
      "username": user.username.trim(),
      "password": user.password,
      "app": "luminator",
      //for attendance tracking
      "ts": DateTime.now().millisecondsSinceEpoch.toString(),
      "latitude": position.latitude,
      "longitude": position.longitude
    };
    // log('coordinates during login : ${position.latitude}, ${position.longitude}');
    try {
      Response response = await Dio().post('$baseURL/api/login/',
          data: data,
          options: Options(
            contentType: Headers.formUrlEncodedContentType,
          ));
      var res = jsonDecode(response.data);
      log(res.toString());
      if (response.statusCode == 200 && res.containsKey('token')) {
        UserModel data = UserModel.fromJson(jsonDecode(response.data));
        await prefs.setString('token', data.token!);
        await prefs.setString('refreshToken', data.refreshToken!);
        await storeLoginFlagsForUserTracking(
            res['isAttendanceCaptureRequired'] ?? false,
            res['isLocationTrackingRequired'] ?? false,
            res['isPPERequired'] ?? false,
            res['locationTrackingInterval'] ?? 0,
            res['employeeId'] ?? '');
        await saveUser(
            jsonEncode(user.toJson()), data.userId ?? '', user.username);
        return 1;
      } else if (res.containsKey('status') && res['status'] != 200) {
        return res;
      } else {
        return 0;
      }
    } catch (e) {
      if (e is DioError) {
        return "500";
      } else {
        return ErrorMessages.tryAgainError;
      }
    }
  }

  Future<dynamic> logoutService(context) async {
    final position = await Geolocator.getCurrentPosition(
      locationSettings: AndroidSettings(accuracy: LocationAccuracy.high),
    );
    var data = FormData.fromMap({
      "ts": DateTime.now().millisecondsSinceEpoch.toString(),
      "latitude": position.latitude,
      "longitude": position.longitude
    });

    try {
      Dio dio = DioClient.dio;
      Response response = await dio.post('$baseURL/api/logout/',
          data: data,
          options: Options(
              // contentType: Headers.formUrlEncodedContentType,
              ));
      var res = jsonDecode(response.data);
      if (response.statusCode == 200) {
        return 1;
      } else if (res.containsKey('status') && res['status'] != 200) {
        return res;
      }
    } catch (e) {
      EasyLoading.dismiss();
      if (e is DioError) {
        log(e.message);
        if (e.error == 'Session expired. Please login again.') {
          return 401;
        }
      }
    }
  }

  // Future<dynamic> getUserId(context, username) async {
  //   // String encodedEmail = Uri.encodeComponent(username);
  //   Dio dio = DioClient.dio;
  //   Map<String, dynamic> headers = {
  //     'Content-Type': 'application/json',
  //   };
  //   try {
  //     Response response = await dio.get(
  //       '$ticketURL/api/auth/user',
  //       options: Options(headers: headers),
  //     );
  //     if (response.statusCode == 200 && response.data != '') {
  //       log('userId :${response.data['id']['id']}');
  //       return response.data['id']['id'];
  //     }
  //   } catch (e) {
  //     EasyLoading.dismiss();
  //     if (e is DioError) {
  //       log(e.message);
  //       if (e.error == 'Session expired. Please login again.') {
  //         return 401;
  //       }
  //     }
  //   }
  // }
}
