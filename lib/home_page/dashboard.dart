import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_speed_dial/flutter_speed_dial.dart';
import 'package:schnell_luminator/attendence_tracking.dart/user_tracking_controller.dart';
import 'package:schnell_luminator/home_page/map_page.dart';
import 'package:schnell_luminator/login_page/login_controller.dart';
import 'package:schnell_luminator/qr_scan_online.dart';
import 'package:schnell_luminator/ticket_notification/ticket_controller.dart';
import 'package:schnell_luminator/utils/asset_folder.dart';
import 'package:schnell_luminator/utils/constants.dart';
import 'package:schnell_luminator/utils/error_messages.dart';
import 'package:schnell_luminator/utils/session.dart';
import 'package:schnell_luminator/utils/utility.dart';
import '../device_page/device_controller.dart';
import '../device_page/device_search.dart';
import '../location_selection/location_controller.dart';
import '../ticket_notification/ticket_list_page.dart';
import '../utils/dialog_box.dart';
import 'package:badges/badges.dart' as badges;

final bottomMenuStateProvider = StateProvider<int>((ref) => 0);

final inappController =
    ChangeNotifierProvider<WebProvider>((ref) => WebProvider());

class WebProvider extends ChangeNotifier {
  // InAppWebViewController webViewController;
  InAppWebViewController? webViewController;
  void setControllerForQr(InAppWebViewController controller) {
    webViewController = controller;
    notifyListeners();
  }
}

class HomePage extends ConsumerWidget {
  HomePage({Key? key}) : super(key: key);
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  final List<Widget> body = [
    Dashboard(),
    const MapPage(),
  ];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var currentPageIndex = ref.watch(bottomMenuStateProvider.state).state;
    String usermail = ref.watch(loginController).userMail;
    String usernmae = usermail.split("@").first.toUpperCase();
    String selectedRegion = ref.watch(locationController).selectedRegion;
    String selectedWard = ref.watch(locationController).selectedWard;
    String wardId = ref.watch(deviceController).userSelectedWardid;
    int selectedBottomNavIndex = ref.watch(bottomMenuStateProvider.state).state;
    String selectedContextId = ref.watch(ticketController).selectedContextId;
    int totalTicketsCount = ref.watch(ticketController).totalTicketsCount;
    String selectedCustomerId = ref.watch(deviceController).selectedCustomerId;
    String selectedZone = ref.watch(locationController).selectedZone;

    return SafeArea(
      child: WillPopScope(
        onWillPop: () {
          return showExitPopup(context);
        },
        child: Scaffold(
          key: _scaffoldKey,
          appBar: AppBar(
              leading: Builder(builder: (BuildContext context) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 9.0),
                  child: IconButton(
                    icon: Icon(
                      Icons.menu,
                      color: Theme.of(context).cardColor,
                    ),
                    onPressed: () {
                      Scaffold.of(context).openDrawer();
                    },
                  ),
                );
              }),
              backgroundColor: Theme.of(context).primaryColor.withOpacity(0.19),
              elevation: 0.0,
              centerTitle: true,
              titleTextStyle: TextStyle(
                color: Theme.of(context).textTheme.bodySmall!.color,
                fontSize: 18.0,
              ),
              title: Text(
                selectedBottomNavIndex == 0 ? "Home Page" : "Map View",
                style: TextStyle(
                    color: Theme.of(context).cardColor,
                    fontWeight: FontWeight.w700,
                    fontSize: 20),
              ),
              actions: <Widget>[
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 9.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      IconButton(
                        icon: Icon(Icons.search,
                            color: Theme.of(context).cardColor),
                        onPressed: () {
                          // if (selectedWard != '') {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => const DeviceSearch()),
                          );
                          // } else {
                          //   wardRequiredAlert(context, ref,
                          //       ErrorMessages.wardRequiredAlertMessage);
                          // }
                        },
                      ),
                      Padding(
                        padding: const EdgeInsets.only(right: 20.0),
                        child: badges.Badge(
                          badgeContent: Text(
                            totalTicketsCount > 99
                                ? '99+'
                                : totalTicketsCount.toString(),
                            style: TextStyle(
                                color: Theme.of(context).canvasColor,
                                fontSize: 11),
                          ),
                          position: badges.BadgePosition.topEnd(top: -10),
                          padding: const EdgeInsets.all(7),
                          child: IconButton(
                            icon: Icon(Icons.checklist,
                                color: Theme.of(context).cardColor),
                            onPressed: () async {
                              Utility.isConnected().then((value) async {
                                if (value) {
                                  bool isLocationTrackingRequi =
                                      await isLocationTrackingRequired();
                                  if (isLocationTrackingRequi) {
                                    bool didAutoLogout = await ref
                                        .read(userTrackingController)
                                        .autoLogout(context, ref);
                                    if (didAutoLogout) return;

                                    final isLocationServiceOn = await Utility
                                        .ensureLocationServiceEnabled();
                                    if (!isLocationServiceOn) {
                                      return; //check if location is on
                                    }
                                  }
                                  EasyLoading.show(
                                      status: 'loading...',
                                      dismissOnTap: false);
                                  ref
                                      .read(ticketController)
                                      .updateSearchText('');
                                  await ref
                                      .read(ticketController)
                                      .isTicketsOfSpecificDeviceOrAsset(false);

                                  if (context.mounted) {
                                    await ref
                                        .read(ticketController)
                                        .getTickets(context, ref,
                                            selectedRegion: selectedRegion)
                                        .then((value) {
                                      EasyLoading.dismiss();
                                      if (context.mounted) {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  const TicketListPage()),
                                        );
                                      }
                                    });
                                  }
                                } else {
                                  if (context.mounted) {
                                    await snackBar(
                                        context,
                                        ErrorMessages.offlineErrorTitle,
                                        ErrorMessages.offlineErrorMessage);
                                  }
                                }
                              });
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ]),
          drawer: customHomeDrawer(
            usernmae,
            usermail,
            context,
            ref,
          ),
          body: body[currentPageIndex],
          bottomNavigationBar: BottomNavigationBar(
            backgroundColor: Theme.of(context).dialogTheme.backgroundColor,
            type: BottomNavigationBarType.fixed,
            currentIndex: ref.read(bottomMenuStateProvider.state).state,
            selectedItemColor: Theme.of(context).cardColor,
            unselectedItemColor: Theme.of(context).unselectedWidgetColor,
            selectedFontSize: 12,
            unselectedFontSize: 12,
            onTap: (v) async {
              bool isLocationTrackingRequi = await isLocationTrackingRequired();
              if (isLocationTrackingRequi) {
                //auto logout after 24 hrs
                bool didAutoLogout = await ref
                    .read(userTrackingController)
                    .autoLogout(context, ref);
                if (didAutoLogout) return;
              }

              if (v == 1) {
                EasyLoading.show(
                  status: '',
                  dismissOnTap: false,
                );
                await ref.read(locationController).fetchCurrentLocation();
                if (context.mounted) {
                  await ref
                      .read(deviceController)
                      .getLatLongBasedonWard(ref, context, selectedContextId);
                }
                EasyLoading.dismiss();
                ref.read(bottomMenuStateProvider.state).state = v;
              } else {
                ref.read(bottomMenuStateProvider.state).state = v;
              }
            },
            items: const [
              BottomNavigationBarItem(
                label: 'Home',
                icon: Icon(Icons.home),
              ),
              BottomNavigationBarItem(label: 'Map', icon: Icon(Icons.map)),
            ],
          ),
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerDocked,
          floatingActionButton: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              FloatingActionButton(
                backgroundColor: Theme.of(context).cardColor,
                onPressed: () {
                  Utility.isConnected().then((value) async {
                    if (value) {
                      try {
                        bool isLocationTrackingRequi =
                            await isLocationTrackingRequired();
                        if (isLocationTrackingRequi) {
                          //auto logout after 24 hrs
                          bool didAutoLogout = await ref
                              .read(userTrackingController)
                              .autoLogout(context, ref);
                          if (didAutoLogout) return;
                        }
                        final isLocationServiceOn =
                            await Utility.ensureLocationServiceEnabled();
                        if (!isLocationServiceOn) {
                          return;
                        } //check if location is on

                        if (context.mounted) {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ScanQRWithCustomScreen(
                                    firstOrSecond: 1, clickEventIsFor: "1"),
                              ));
                        }
                      } catch (e) {
                        log(e.toString());
                      }
                    } else {
                      if (context.mounted) {
                        await snackBar(context, ErrorMessages.offlineErrorTitle,
                            ErrorMessages.offlineErrorMessage);
                      }
                    }
                  });
                },
                child: const Icon(
                  Icons.qr_code,
                  size: 28,
                ),
              ),
              const SizedBox(
                width: 10,
              ),
              SpeedDial(
                icon: Icons.add,
                iconTheme: const IconThemeData(size: 33),
                curve: Curves.elasticInOut,
                activeIcon: Icons.close,
                backgroundColor: Theme.of(context).cardColor,
                foregroundColor: Theme.of(context).canvasColor,
                overlayColor: Colors.black,
                overlayOpacity: 0.4,
                buttonSize: const Size(55, 55),
                switchLabelPosition: false,
                children: [
                  SpeedDialChild(
                    child: Image.file(
                      Assets.poleInstalltransparent,
                      height: 25,
                    ),
                    label: 'Pole Install',
                    backgroundColor: Theme.of(context).cardColor,
                    onTap: () async {
                      if (selectedWard != '') {
                        if (context.mounted) {
                          ref.read(deviceController).openOtherApp(
                                ref,
                                context,
                                "com.schnelliot.polevault",
                                selectedCustomerId,
                                selectedRegion,
                                selectedZone,
                                selectedWard,
                                wardId,
                                usermail,
                              );
                        }
                      } else {
                        wardRequiredAlert(context, ref,
                            ErrorMessages.wardRequiredAlertMessage);
                      }
                    },
                  ),
                  SpeedDialChild(
                    child: Image.file(
                      Assets.lampInstallation,
                      height: 25,
                    ),
                    label: 'Lamp Install',
                    backgroundColor: Theme.of(context).cardColor,
                    onTap: () async {
                      Utility.isConnected().then((value) async {
                        if (value) {
                          if (selectedWard != '') {
                            if (context.mounted) {
                              await ref
                                  .read(deviceController)
                                  .lampCount(ref, context, wardId, usermail);
                            }
                          } else {
                            if (context.mounted) {
                              wardRequiredAlert(context, ref,
                                  ErrorMessages.wardRequiredAlertMessage);
                            }
                          }
                        } else {
                          if (context.mounted) {
                            await snackBar(
                                context,
                                ErrorMessages.offlineErrorTitle,
                                ErrorMessages.offlineErrorMessage);
                          }
                        }
                      });
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<bool> showExitPopup(context) async {
    var bg = Theme.of(context).dialogTheme.backgroundColor;
    return await showDialog(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: bg,
            content: Container(
              height: 100,
              color: bg,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Are you sure you want to exit?",
                    style: TextStyle(
                        color: Theme.of(context).secondaryHeaderColor),
                  ),
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            if (kDebugMode) {
                              log('yes selected');
                            }
                            exit(0);
                          },
                          style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  Theme.of(context).indicatorColor),
                          child: Text("Yes",
                              style: TextStyle(
                                  color: Theme.of(context).canvasColor)),
                        ),
                      ),
                      const SizedBox(width: 15),
                      Expanded(
                          child: ElevatedButton(
                              onPressed: () {
                                if (kDebugMode) {
                                  log('no selected');
                                }
                                Navigator.of(context).pop();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Theme.of(context)
                                    .textTheme
                                    .bodySmall!
                                    .color,
                              ),
                              child: Text("No",
                                  style: TextStyle(
                                    color: Theme.of(context).canvasColor,
                                  ))))
                    ],
                  )
                ],
              ),
            ),
          );
        });
  }
}

class Dashboard extends ConsumerWidget {
  Dashboard({Key? key}) : super(key: key);

  final webViewKey = GlobalKey();

  InAppWebViewGroupOptions options = InAppWebViewGroupOptions(
    crossPlatform: InAppWebViewOptions(
      useShouldOverrideUrlLoading: true,
      mediaPlaybackRequiresUserGesture: false,
      javaScriptEnabled: true,
      supportZoom: false,
      cacheEnabled: true,
    ),
    android: AndroidInAppWebViewOptions(
      useHybridComposition: true,
      thirdPartyCookiesEnabled: true,
    ),
    ios: IOSInAppWebViewOptions(
      allowsInlineMediaPlayback: true,
      allowsBackForwardNavigationGestures: false,
    ),
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    String selectedCustomer = ref.watch(locationController).selectedCustomer;
    String selectedRegion = ref.watch(locationController).selectedRegion;
    String selectedZone = ref.watch(locationController).selectedZone;
    String selectedWard = ref.watch(locationController).selectedWard;
    String accessTokenForWebView =
        ref.watch(loginController).accessTokenForWebView;
    String stateParamsForWeView =
        ref.watch(loginController).stateParamsForWeView;

    // int totalTicketsCount = ref.watch(ticketController).totalTicketsCount;
    log({
      'insidedashboardclass accesstoken: $accessTokenForWebView, stateparams:$stateParamsForWeView'
    }.toString());
    final screenSize = MediaQuery.of(context).size;
    final webViewHeight = screenSize.height / 1.45;
    // final webViewHeight = screenSize.height / 1.71;
    Future<void> handleDeviceId(viewname, deviceId) async {
      log('Updated selectedElementFromWebView: $deviceId');

      ///The below if conditiotn is prevent maintenance page navigation for the auto regenerated lamp id's
      if (deviceId.toString().length < 32) {
        await ref
            .read(deviceController)
            .webViewMaintenance(ref, context, viewname, deviceId);
      }
      EasyLoading.dismiss();
    }

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: SelectedLocationData(
            selectedCustomer: selectedCustomer,
            selectedRegion: selectedRegion,
            selectedZone: selectedZone,
            selectedWard: selectedWard,
          ),
        ),
        SizedBox(
          height: webViewHeight,
          width: screenSize.width,
          child: InAppWebView(
            key: webViewKey,
            initialUrlRequest: URLRequest(
              url: WebUri(
                '$ticketURL/dashboard/$webviewDashboardId?accessToken=$accessTokenForWebView&state=$stateParamsForWeView',
              ),
            ),
            initialOptions: options,
            onWebViewCreated: (webViewController) {
              ref.read(inappController).setControllerForQr(webViewController);

              log("onWebViewCreated");
              webViewController.addJavaScriptHandler(
                handlerName: 'SelectedDeviceHandler',
                callback: (args) {
                  final details = jsonDecode(args[0]);

                  log("Received device value from web in handler: $details");
                  final viewname = details['viewName'];
                  final deviceId = details['deviceId'];
                  EasyLoading.show(
                    status: '',
                    dismissOnTap: false,
                  );
                  handleDeviceId(viewname, deviceId);
                },
              );
            },
            onLoadStart: (controller, url) async {
              log('onLoadStart: $url');
              await controller.evaluateJavascript(source: '''
                document.addEventListener('click', function(e) {
                      setTimeout(() => {
                  var storedData = window.localStorage.getItem('details');
                  console.log('Current selected storedData:', storedData);
                  var details = JSON.parse(storedData);
                  var viewName = details.viewName;
                  console.log('Current selected viewName from web in js:', viewName);
                  var deviceId = details.deviceId;
                  console.log('Current selected deviceId from web in js:', deviceId);                    
                   if (storedData !== null) {
                window.flutter_inappwebview.callHandler('SelectedDeviceHandler',storedData);
                window.localStorage.removeItem('details');
              }
                  }, 50);
                }, true);
              ''');
            },
            onReceivedHttpError: (controller, request, errorResponse) {
              if (errorResponse.statusCode == 401) {
                log('status code onReceivedHttpError ${errorResponse.statusCode}');
                log('Session expired due to token expiration');
                // tokenExpired(context, ref);
              } else {
                log('HTTP error: ${errorResponse.statusCode}');
              }
            },
            onLoadError: (controller, url, code, message) {
              log('onLoadError: $url, code: $code, message: $message');
            },
            androidOnPermissionRequest: (controller, origin, resources) async {
              log('androidOnPermissionRequest origin: $origin, resources: $resources');
              return PermissionRequestResponse(
                resources: resources,
                action: PermissionRequestResponseAction.GRANT,
              );
            },
          ),
        ),
      ],
    );
  }
}
