import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:schnell_luminator/attendence_tracking.dart/user_tracking_controller.dart';
import 'package:schnell_luminator/home_page/dashboard.dart';
import 'package:schnell_luminator/utils/error_messages.dart';
import 'package:schnell_luminator/utils/session.dart';
import '../utils/constants.dart';
import '../utils/dialog_box.dart';
import '../utils/utility.dart';
import 'device_controller.dart';

class DeviceSearch extends ConsumerWidget {
  const DeviceSearch({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    TextEditingController searchController = TextEditingController();
    List searchResult = ref.watch(deviceController).deviceSearchList;
    List filteredList = searchResult
        .where((item) =>
            item['type'] == "ilm" ||
            item['type'] == "gw" ||
            item['type'] == "ccms" ||
            item['type'] == "hub" ||
            item['type'] == "lamp" ||
            item['type'] == "pole" ||
            item['type'] == "nic" ||
            item['type'] == "smslc" ||
            item['type'] == 'ilm-4g')
        .toList();
    filteredList.sort((a, b) => a["name"].compareTo(b["name"]));

    return SafeArea(
      child: WillPopScope(
        onWillPop: () async {
          ref.read(deviceController).emptyDeviceSearchList();
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => HomePage(),
            ),
          );
          return true;
        },
        child: Scaffold(
          appBar: AppBar(
            leading: IconButton(
              icon: Icon(
                Icons.arrow_back,
                color: Theme.of(context).cardColor,
              ),
              onPressed: () async {
                ref.read(deviceController).emptyDeviceSearchList();
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => HomePage(),
                  ),
                );
              },
            ),
            title: Padding(
              padding: const EdgeInsets.only(top: 18.0),
              child: Text(
                'Search Device/Asset',
                style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).cardColor),
              ),
            ),
            backgroundColor: Colors.transparent,
            elevation: 0,
            centerTitle: true,
          ),
          body: SafeArea(
            child: Column(
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.all(18.0),
                  child: TextFormField(
                    maxLength: 20,
                    controller: searchController,
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.all(5.0),
                      hintText: 'Search for ILM,CCMS,HUB,LAMP,GW,POLE',
                      counterStyle:
                          TextStyle(color: Theme.of(context).primaryColor),
                      hintStyle: Theme.of(context)
                          .textTheme
                          .titleLarge!
                          .copyWith(
                              fontSize: 14.0,
                              color: Theme.of(context).primaryColor),
                      suffixIcon: Padding(
                          padding: const EdgeInsets.only(left: 5.0, right: 10),
                          child: IconButton(
                              onPressed: () {
                                Utility.isConnected().then((value) async {
                                  if (value) {
                                    bool isLocationTrackingRequi =
                                        await isLocationTrackingRequired();
                                    if (isLocationTrackingRequi) {
                                      //auto logout after 24 hrs
                                      bool didAutoLogout = await ref
                                          .read(userTrackingController)
                                          .autoLogout(context, ref);
                                      if (didAutoLogout) return;
                                    }
                                    final isLocationServiceOn = await Utility
                                        .ensureLocationServiceEnabled();
                                    if (!isLocationServiceOn) {
                                      return;
                                    } //check if location is on

                                    var en = searchController.text;

                                    if (en.length < 3) {
                                      if (context.mounted) {
                                        await snackBar(
                                            context,
                                            ErrorMessages
                                                .searchCharLimitWarningTitle,
                                            ErrorMessages
                                                .searchCharLimitWarningMessage);
                                      }
                                    } else {
                                      if (context.mounted) {
                                        await ref
                                            .read(deviceController)
                                            .deviceListSearch(ref, context,
                                                en.replaceAll("#", ""));
                                      }
                                      searchController.text = '';
                                    }
                                  } else {
                                    if (context.mounted) {
                                      await snackBar(
                                          context,
                                          ErrorMessages.offlineErrorTitle,
                                          ErrorMessages.offlineErrorMessage);
                                    }
                                  }
                                });
                              },
                              icon: Icon(Icons.search,
                                  size: 30,
                                  color: Theme.of(context).cardColor))),
                      border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                          borderSide: BorderSide.none),
                      filled: true,
                      fillColor:
                          Theme.of(context).primaryColor.withOpacity(0.19),
                    ),
                    style: Theme.of(context)
                        .textTheme
                        .bodySmall!
                        .copyWith(fontSize: 16.0),
                    keyboardType: TextInputType.text,
                  ),
                ),
                const SizedBox(
                  height: 5,
                ),
                Expanded(
                    child: ListView.builder(
                  itemCount: filteredList.length,
                  itemBuilder: (ctx, index) => _deviceListTile(
                      ctx,
                      ref,
                      filteredList[index]['name'],
                      filteredList[index]['type'],
                      "1"),
                )),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _deviceListTile(context, WidgetRef ref, String searchResult,
      String type, clickEventIsFor) {
    File imageData = imageLocation[type]!;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      child: GestureDetector(
        onTap: () async {
          EasyLoading.dismiss();
          await ref.read(deviceController).showSelectedDevice(
              ref, context, searchResult, type, clickEventIsFor);
          ref.read(deviceController).emptyDeviceSearchList();
        },
        child: ListTile(
          title: Row(
            children: [
              Text(
                searchResult,
                style: TextStyle(
                    color: Theme.of(context).textTheme.bodySmall!.color),
              ),
            ],
          ),
          subtitle: Text(
            type,
            style: TextStyle(color: Theme.of(context).cardColor),
          ),
          leading: Container(
            width: 50,
            margin: const EdgeInsets.all(5),
            child: Image.file(
              imageData,
            ),
          ),
        ),
      ),
    );
  }
}
