import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:schnell_luminator/device_page/device_controller.dart';
import 'package:schnell_luminator/home_page/dashboard.dart';
import 'package:schnell_luminator/location_selection/location_controller.dart';
import 'package:schnell_luminator/qr_scan_online.dart';
import 'package:schnell_luminator/utils/constants.dart';
import 'package:schnell_luminator/utils/dialog_box.dart';
import 'package:schnell_luminator/utils/error_messages.dart';
import 'package:schnell_luminator/utils/utility.dart';

class PoleScanPage extends ConsumerWidget {
  const PoleScanPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    String selectedRegion = ref.watch(locationController).selectedRegion;
    String selectedZone = ref.watch(locationController).selectedZone;
    String selectedWard = ref.watch(locationController).selectedWard;
    int newLampCount = ref.watch(deviceController).newLampCount;
    List lmpHistoryData = ref.watch(deviceController).lmpHistoryData;
    int lampSpecificUserCount =
        ref.watch(deviceController).lampSpecificUserCount;
    var todaysDate =
        '${DateTime.now().day.toString().length == 1 ? '0${DateTime.now().day.toString()}' : DateTime.now().day.toString()}-${DateTime.now().month.toString().length == 1 ? '0${DateTime.now().month.toString()}' : DateTime.now().month.toString()}-${DateTime.now().year.toString()}';

    return SafeArea(
      child: WillPopScope(
        onWillPop: () async {
          // Navigator.pop(context);

          ref.read(bottomMenuStateProvider.state).state = 0;
          await Navigator.of(context).pushNamedAndRemoveUntil(
              homeRoute, (Route<dynamic> route) => false);
          return true;
        },
        child: Scaffold(
          appBar: AppBar(
            leading: Builder(builder: (BuildContext context) {
              return IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: Theme.of(context).cardColor,
                ),
                onPressed: () async {
                  ref.read(bottomMenuStateProvider.state).state = 0;
                  await Navigator.of(context).pushNamedAndRemoveUntil(
                      homeRoute, (Route<dynamic> route) => false);
                },
              );
            }),
            backgroundColor: Theme.of(context).primaryColor.withOpacity(0.19),
            elevation: 0.0,
            centerTitle: true,
            titleTextStyle: TextStyle(
              color: Theme.of(context).textTheme.bodySmall!.color,
              fontSize: 18.0,
            ),
            title: Text(
              "LAMP INSTALLATION",
              style: TextStyle(
                  color: Theme.of(context).cardColor,
                  fontWeight: FontWeight.w700),
            ),
          ),
          body: Column(children: [
            const SizedBox(
              height: 8,
            ),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  Container(
                    width: 235,
                    margin: const EdgeInsets.only(
                        left: 10, right: 5, top: 5, bottom: 5),
                    height: 50,
                    decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(18)),
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.all(6.0),
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: FittedBox(
                            fit: BoxFit.contain,
                            child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Icon(Icons.location_on_outlined,
                                      color: Theme.of(context).cardColor),
                                  const SizedBox(
                                    width: 5,
                                  ),
                                  Text(
                                    selectedRegion,
                                    style: TextStyle(
                                        color: Theme.of(context).cardColor,
                                        fontWeight: FontWeight.bold),
                                  ),
                                  Icon(Icons.chevron_right_outlined,
                                      color: Theme.of(context).cardColor),
                                  Text(
                                    selectedZone,
                                    style: TextStyle(
                                        color: Theme.of(context).cardColor,
                                        fontWeight: FontWeight.bold),
                                  ),
                                  Icon(Icons.chevron_right_outlined,
                                      color: Theme.of(context).cardColor),
                                  Text(
                                    selectedWard,
                                    style: TextStyle(
                                        color: Theme.of(context).cardColor,
                                        fontWeight: FontWeight.bold),
                                  )
                                ]),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Container(
                    width: 60,
                    margin: const EdgeInsets.only(
                        left: 0, right: 5, top: 5, bottom: 5),
                    height: 45,
                    decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withOpacity(0.8),
                        borderRadius: BorderRadius.circular(10)),
                    child: Center(
                      child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.business,
                              color: Theme.of(context).cardColor,
                              size: 21,
                            ),
                            const SizedBox(
                              width: 3,
                            ),
                            Flexible(
                              child: FittedBox(
                                fit: BoxFit.scaleDown,
                                child: Text(
                                  newLampCount.toString(),
                                  style: TextStyle(
                                    fontSize: 17,
                                    color: Theme.of(context).cardColor,
                                  ),
                                ),
                              ),
                            )
                          ]),
                    ),
                  ),
                  Container(
                    width: 60,
                    margin: const EdgeInsets.only(
                        left: 0, right: 5, top: 5, bottom: 5),
                    height: 45,
                    decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        borderRadius: BorderRadius.circular(10)),
                    child: Center(
                      child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.person_2_outlined,
                              size: 21,
                              color: Theme.of(context).cardColor,
                            ),
                            const SizedBox(
                              width: 3,
                            ),
                            Flexible(
                              child: FittedBox(
                                fit: BoxFit.scaleDown,
                                child: Text(
                                  lampSpecificUserCount.toString(),
                                  style: TextStyle(
                                    fontSize: 17,
                                    color: Theme.of(context).cardColor,
                                  ),
                                ),
                              ),
                            )
                          ]),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 50,
            ),
            Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: MediaQuery.of(context).size.width * 0.11,
                    ),
                    Text(
                      'Lamp Installation History for this user',
                      style: TextStyle(
                          fontSize: 16,
                          color: Theme.of(context).cardColor,
                          fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
                SizedBox(
                  width: MediaQuery.of(context).size.width / 1.1,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.8),
                      border: Border.all(
                        color: Theme.of(context).primaryColor.withOpacity(0.8),
                        width: 1,
                      ),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(10),
                          topRight: Radius.circular(10)),
                    ),
                    child: Table(
                      border: TableBorder.symmetric(
                        inside: BorderSide(
                          width: 1,
                          color:
                              Theme.of(context).primaryColor.withOpacity(0.19),
                        ),
                      ),
                      children: [
                        TableRow(children: [
                          TableCell(
                              child: Padding(
                            padding: const EdgeInsets.all(6.0),
                            child: Text(
                              "Date",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                  color: Theme.of(context).cardColor,
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold),
                            ),
                          )),
                          TableCell(
                              child: Padding(
                            padding: const EdgeInsets.all(6.0),
                            child: Text(
                              "Lamp Installed Count",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                  color: Theme.of(context).cardColor,
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold),
                            ),
                          )),
                        ]),
                      ],
                    ),
                  ),
                ),
                Column(
                  children: [
                    SizedBox(
                      width: MediaQuery.of(context).size.width / 1.1,
                      child: ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: lmpHistoryData.length,
                        itemBuilder: (context, index) {
                          final myObject = lmpHistoryData[index];
                          final keys = myObject.keys.toList();
                          final values = myObject.values.toList();
                          return Container(
                            decoration: BoxDecoration(
                                color: Theme.of(context)
                                    .primaryColor
                                    .withOpacity(0.19),
                                border: Border.all(
                                  color: Theme.of(context)
                                      .dialogTheme
                                      .backgroundColor!,
                                  width: 0.5,
                                ),
                                borderRadius: index == lmpHistoryData.length - 1
                                    ? const BorderRadius.only(
                                        bottomLeft: Radius.circular(10),
                                        bottomRight: Radius.circular(10))
                                    : const BorderRadius.only(
                                        bottomLeft: Radius.circular(0),
                                        bottomRight: Radius.circular(0))),
                            child: Table(
                              border: TableBorder.symmetric(
                                  inside: BorderSide(
                                width: 1,
                                color: Theme.of(context)
                                    .dialogTheme
                                    .backgroundColor!,
                              )),
                              children: List<TableRow>.generate(keys.length,
                                  (rowIndex) {
                                final key = keys[rowIndex];
                                final value = values[rowIndex];
                                return TableRow(children: [
                                  TableCell(
                                      child: Padding(
                                    padding: const EdgeInsets.all(10.0),
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          key == todaysDate ? "Today" : key,
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            color: Theme.of(context)
                                                .secondaryHeaderColor,
                                          ),
                                        )
                                      ],
                                    ),
                                  )),
                                  TableCell(
                                      child: Padding(
                                    padding: const EdgeInsets.all(10.0),
                                    child: Text(
                                      value.toString(),
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: Theme.of(context)
                                            .secondaryHeaderColor,
                                      ),
                                    ),
                                  )),
                                ]);
                              }),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 57,
                ),
                SizedBox(
                    height: 50,
                    width: 330,
                    child: Material(
                      elevation: 5,
                      color: Theme.of(context).primaryColor,
                      borderRadius: BorderRadius.circular(20.0),
                      child: MaterialButton(
                        height: 60,
                        onPressed: () async {
                          Utility.isConnected().then((value) async {
                            if (value) {
                              if (context.mounted) {
                                await Navigator.of(context)
                                    .push(MaterialPageRoute(
                                  builder: (context) => ScanQRWithCustomScreen(
                                      firstOrSecond: 1,
                                      clickEventIsFor: "ScanAPole"),
                                ));
                              }
                            } else {
                              if (context.mounted) {
                                await snackBar(
                                    context,
                                    ErrorMessages.offlineErrorTitle,
                                    ErrorMessages.offlineErrorMessage);
                              }
                            }
                          });
                        },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'Scan a Pole',
                              style: TextStyle(
                                  color: Theme.of(context).canvasColor,
                                  fontSize: 20),
                            ),
                            const SizedBox(
                              width: 50,
                            ),
                            Icon(
                              Icons.qr_code_scanner_outlined,
                              color: Theme.of(context).canvasColor,
                              size: 29,
                            ),
                          ],
                        ),
                      ),
                    )),
              ],
            ),
          ]),
        ),
      ),
    );
  }
}
