import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:schnell_luminator/device_page/device_controller.dart';
import 'package:schnell_luminator/device_page/lamp/lamp_scan_page.dart';
import 'package:schnell_luminator/location_selection/location_controller.dart';
import 'package:schnell_luminator/ticket_notification/ticket_list_page.dart';
import 'package:schnell_luminator/utils/asset_folder.dart';
import 'package:schnell_luminator/utils/dialog_box.dart';
import 'package:schnell_luminator/utils/error_messages.dart';
import 'package:schnell_luminator/utils/session.dart';
import 'package:schnell_luminator/utils/utility.dart';
import '../../home_page/dashboard.dart';
import '../../qr_scan_online.dart';
import '../../utils/scroll_indicator.dart';
import '../device_search.dart';

class PoleDetails extends ConsumerWidget {
  final String? navigationPath;
  PoleDetails({super.key, this.navigationPath});

  final ScrollController scrollViewController = ScrollController();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    String selectedRegion = ref.watch(locationController).selectedRegion;
    String selectedZone = ref.watch(locationController).selectedZone;
    String selectedWard = ref.watch(locationController).selectedWard;
    String poleLocation = ref.watch(deviceController).poleLocation;
    int armCount = ref.watch(deviceController).armCount;
    List<dynamic> lampProfileList = [];
    if (armCount != 0) {
      String lampProfiles = ref.watch(deviceController).lampProfiles;
      if (lampProfiles.isNotEmpty) {
        lampProfileList = jsonDecode(lampProfiles);
      }
    }
    String poleLat = ref.watch(deviceController).poleLat;
    String poleLong = ref.watch(deviceController).poleLong;
    String poleName = ref.watch(deviceController).poleName;
    String poleRegion = ref.watch(deviceController).poleRegion;
    String poleWardName = ref.watch(deviceController).poleWardName;
    String poleZoneName = ref.watch(deviceController).poleZoneName;
    // Color disabledColor = const Color.fromARGB(255, 171, 171, 171);
    // Color disabledDarkColor = const Color.fromARGB(255, 214, 211, 211);
    List lampNamesInRelation = ref.watch(deviceController).lampNamesInRelation;
    List ilmNamesInRelation = ref.watch(deviceController).ilmNamesInRelation;
    List lampLightPointIdInRelation =
        ref.watch(deviceController).lampLightPointIdInRelation;
    List<dynamic> recommendedWattage =
        ref.watch(deviceController).recommendedWattage;

    return SafeArea(
      child: WillPopScope(
        onWillPop: () async {
          // Navigator.of(context).pop();
          if (selectedWard != "") {
            if (navigationPath == "isSearch") {
              ref.read(deviceController).emptyDeviceSearchList();
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const DeviceSearch()));
            } else if (navigationPath == "isTicket") {
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const TicketListPage()));
            } else if (navigationPath == "isWebview") {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => HomePage(),
                ),
              );
            } else {
              await Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const PoleScanPage()));
            }
          } else if (selectedWard == '') {
            if (navigationPath == "isSearch") {
              ref.read(deviceController).emptyDeviceSearchList();
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const DeviceSearch()));
            } else if (navigationPath == "isTicket") {
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const TicketListPage()));
              // } else if (navigationPath == "isWebview") {
              // Navigator.pop(context);
            } else {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => HomePage(),
                ),
              );
            }
          }

          return false;
        },
        child: Scaffold(
          appBar: AppBar(
            leading: Builder(builder: (BuildContext context) {
              return IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: Theme.of(context).cardColor,
                ),
                onPressed: () async {
                  // Navigator.pop(context);
                  if (selectedWard != "") {
                    if (navigationPath == "isSearch") {
                      ref.read(deviceController).emptyDeviceSearchList();
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => const DeviceSearch()));
                    } else if (navigationPath == "isTicket") {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => const TicketListPage()));
                    } else {
                      await Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => const PoleScanPage()));
                    }
                  } else if (selectedWard == '') {
                    if (navigationPath == "isSearch") {
                      ref.read(deviceController).emptyDeviceSearchList();
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => const DeviceSearch()));
                    } else if (navigationPath == "isTicket") {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => const TicketListPage()));
                    } else {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => HomePage(),
                        ),
                      );
                    }
                  }
                },
              );
            }),
            backgroundColor: Theme.of(context).primaryColor.withOpacity(0.19),
            elevation: 0.0,
            centerTitle: true,
            titleTextStyle: TextStyle(
              color: Theme.of(context).textTheme.bodySmall!.color,
              fontSize: 18.0,
            ),
            title: Text(
              "POLE MAINTENANCE",
              style: TextStyle(color: Theme.of(context).cardColor),
            ),
          ),
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerDocked,
          floatingActionButton:
              ScrollIndicator(controller: scrollViewController),
          body: SingleChildScrollView(
            controller: scrollViewController,
            child: Column(children: [
              const SizedBox(
                height: 8,
              ),
              // if (selectedWard != '')
              GestureDetector(
                child: Container(
                  // width: 290,
                  margin: const EdgeInsets.only(
                      left: 15, right: 15, top: 5, bottom: 5),
                  height: 50,
                  decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.8),
                      borderRadius: BorderRadius.circular(18)),
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.all(6.0),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: FittedBox(
                          fit: BoxFit.contain,
                          child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Icon(Icons.location_on_outlined,
                                    color: Theme.of(context).canvasColor),
                                const SizedBox(
                                  width: 5,
                                ),

                                //the below condition : selectedWard != '' is used for displaying pole context whenever user unselect the region/zone/ward and navigate to pole maintenance screen
                                Text(
                                  selectedWard != ''
                                      ? selectedRegion
                                      : poleRegion,
                                  style: TextStyle(
                                      color: Theme.of(context).canvasColor,
                                      fontWeight: FontWeight.bold),
                                ),
                                Icon(Icons.chevron_right_outlined,
                                    color: Theme.of(context).canvasColor),
                                Text(
                                  selectedWard != ''
                                      ? selectedZone
                                      : poleZoneName,
                                  style: TextStyle(
                                      color: Theme.of(context).canvasColor,
                                      fontWeight: FontWeight.bold),
                                ),
                                Icon(Icons.chevron_right_outlined,
                                    color: Theme.of(context).canvasColor),
                                Text(
                                  selectedWard != ''
                                      ? selectedWard
                                      : poleWardName,
                                  style: TextStyle(
                                      color: Theme.of(context).canvasColor,
                                      fontWeight: FontWeight.bold),
                                )
                              ]),
                        ),
                      ),
                    ),
                  ),
                ),
                onTap: () {},
              ),
              const SizedBox(
                height: 10,
              ),
              Column(
                children: [
                  Center(
                      child: RichText(
                    text: TextSpan(
                      text: 'Pole # ',
                      style: TextStyle(
                        color: Theme.of(context).secondaryHeaderColor,
                        fontSize: 18,
                      ),
                      children: <TextSpan>[
                        TextSpan(
                          text: poleName.toString(),
                          style: TextStyle(
                              color: Theme.of(context).secondaryHeaderColor,
                              fontSize: 18,
                              fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  )),
                  const SizedBox(
                    height: 20,
                  ),
                  SizedBox(
                    width: MediaQuery.of(context).size.width * 0.9,
                    child: InputDecorator(
                      decoration: InputDecoration(
                          label: RichText(
                              text: TextSpan(
                            text: 'Location',
                            style: TextStyle(
                                fontSize: 18,
                                color: Theme.of(context).cardColor,
                                fontWeight: FontWeight.bold),
                          )),
                          border: const OutlineInputBorder()),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          selectedWard != poleWardName
                              ? FittedBox(
                                  child: Container(
                                    height: 50,
                                    decoration: BoxDecoration(
                                        color: Theme.of(context)
                                            .primaryColor
                                            .withOpacity(0.8),
                                        borderRadius:
                                            BorderRadius.circular(18)),
                                    child: Center(
                                      child: Row(
                                        children: [
                                          const SizedBox(
                                            width: 7.5,
                                          ),
                                          Icon(Icons.location_on_outlined,
                                              color: Theme.of(context)
                                                  .canvasColor),
                                          const SizedBox(
                                            width: 7.5,
                                          ),
                                          Text(
                                            '$poleRegion > $poleZoneName > $poleWardName',
                                            style: TextStyle(
                                                color: Theme.of(context)
                                                    .canvasColor,
                                                fontWeight: FontWeight.bold),
                                          ),
                                          const SizedBox(
                                            width: 15,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                )
                              : Container(),
                          selectedWard != poleWardName
                              ? const SizedBox(
                                  height: 2,
                                )
                              : Container(),
                          poleLocation != ''
                              ? Text(
                                  poleLocation,
                                  style: TextStyle(
                                      color: Theme.of(context)
                                          .secondaryHeaderColor,
                                      fontSize: 14),
                                )
                              : Container(),
                          poleLocation != ''
                              ? const SizedBox(
                                  height: 2,
                                )
                              : Container(),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              const Text(
                                'Latitude : ',
                                // textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 13,
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  poleLat,
                                  style: const TextStyle(
                                      fontSize: 13,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              const Text(
                                'Longitude : ',
                                style: TextStyle(
                                  fontSize: 13,
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  poleLong,
                                  style: const TextStyle(
                                      fontSize: 13,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  if (armCount != 0)
                    SizedBox(
                      width: MediaQuery.of(context).size.width * 0.9,
                      child: InputDecorator(
                        decoration: InputDecoration(
                            label: RichText(
                                text: TextSpan(
                              text: 'Old Lamp Type',
                              style: TextStyle(
                                  fontSize: 18,
                                  color: Theme.of(context).cardColor,
                                  fontWeight: FontWeight.bold),
                            )),
                            border: const OutlineInputBorder()),
                        child: Wrap(
                          runSpacing: 6,
                          spacing: 3,
                          alignment: WrapAlignment.start,
                          direction: Axis.horizontal,
                          children:
                              List.generate(lampProfileList.length, (index) {
                            String lampType = '';
                            String lampWatt = '';
                            if (index < lampProfileList.length) {
                              lampType =
                                  lampProfileList[index]['type'].toString();
                              lampWatt =
                                  lampProfileList[index]['watts'].toString();
                            }
                            return _buildBubble(
                                context, "$lampType-$lampWatt W");
                          }),
                        ),
                      ),
                    ),
                  const SizedBox(
                    height: 20,
                  ),
                  if (armCount != 0)
                    SizedBox(
                      width: MediaQuery.of(context).size.width * 0.9,
                      child: InputDecorator(
                        decoration: InputDecoration(
                            label: RichText(
                                text: TextSpan(
                              text: 'Recommended Lamp type',
                              style: TextStyle(
                                  fontSize: 18,
                                  color: Theme.of(context).cardColor,
                                  fontWeight: FontWeight.bold),
                            )),
                            border: const OutlineInputBorder()),
                        child: Wrap(
                          runSpacing: 6,
                          spacing: 3,
                          alignment: WrapAlignment.start,
                          direction: Axis.horizontal,
                          children:
                              List.generate(recommendedWattage.length, (index) {
                            String recommLampType = '';
                            int recommLampWatt = 0;
                            if (index < recommendedWattage.length) {
                              recommLampType =
                                  recommendedWattage[index]['type'].toString();
                              recommLampWatt = recommendedWattage[index]
                                  ['recommended_wattage'];
                            }

                            return _buildBubble(
                                context, "$recommLampType-$recommLampWatt W");
                          }),
                        ),
                      ),
                    ),
                  if (selectedWard == poleWardName)
                    const SizedBox(
                      height: 20,
                    ),
                  if (selectedWard != poleWardName)
                    Center(
                      child: Container(
                        width: MediaQuery.of(context).size.width * 0.9,
                        margin: const EdgeInsets.only(bottom: 20, top: 20),
                        padding: const EdgeInsets.all(10.0),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border.all(
                            color: Colors.red,
                            width: 2.0,
                          ),
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Icon(
                              Icons.warning,
                              color: Colors.red,
                              size: 24.0,
                            ),
                            const SizedBox(width: 10),
                            Expanded(
                              child: Text(
                                selectedWard != ''
                                    ? 'The selected project and location of the pole are different. Please select the correct project and try again.'
                                    : "Please select the project /Region to proceed.",
                                style: const TextStyle(
                                  fontSize: 15.0,
                                  color: Colors.black,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  Column(
                    children: [
                      Wrap(
                        runSpacing: 0,
                        spacing: 10,
                        alignment: WrapAlignment.center,
                        direction: Axis.vertical,
                        children: List.generate(armCount, (index) {
                          String lampLabel = '';
                          String ilmLabel = '';
                          String lampLightPointId = '';
                          String brand = '';
                          int wattage = 0;
                          String mfgYear = '';
                          if (index < lampNamesInRelation.length) {
                            lampLabel = lampNamesInRelation[index];
                            ilmLabel = ilmNamesInRelation[index];
                            lampLightPointId =
                                lampLightPointIdInRelation[index];
                            if (lampLabel.isNotEmpty && lampLabel.length < 32) {
                              List<String> parts = lampLabel.split('-');
                              String unAbbreviatedBrand = parts[0];
                              String wattsInString = parts[1];
                              mfgYear = parts[4];
                              wattage = int.parse(wattsInString);
                              if (unAbbreviatedBrand == 'HA') {
                                brand = "Havells";
                              } else if (unAbbreviatedBrand == 'SI') {
                                brand = 'Signify';
                              } else if (unAbbreviatedBrand == 'OR') {
                                brand = 'Orient';
                              } else if (unAbbreviatedBrand == 'BA') {
                                brand = 'Bajaj';
                              } else if (unAbbreviatedBrand == 'HI') {
                                brand = 'Hitro';
                              } else {
                                brand = unAbbreviatedBrand;
                              }
                            }
                          }
                          return Container(
                            padding: const EdgeInsets.all(7.0),
                            width: MediaQuery.of(context).size.width * 0.9,
                            decoration: BoxDecoration(
                              color: Theme.of(context).canvasColor,
                              border: Border.all(
                                  color: Theme.of(context).primaryColor,
                                  width: 1.0),
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (lampLabel.isNotEmpty)
                                  Text(
                                    "Installed Lamp",
                                    style: TextStyle(
                                      fontSize: 13,
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ),
                                //container for already installed lamp
                                lampLabel.isNotEmpty
                                    ? GestureDetector(
                                        onTap: () {
                                          selectedWard == poleWardName
                                              ? Utility.isConnected()
                                                  .then((value) async {
                                                  if (value) {
                                                    ref
                                                        .read(deviceController)
                                                        .userSelectedDeviceOrAsset(
                                                            lampLabel);

                                                    if (context.mounted) {
                                                      await ref
                                                          .read(
                                                              deviceController)
                                                          .installedLampNavigation(
                                                              ref,
                                                              context,
                                                              lampLabel,
                                                              1,
                                                              "1");
                                                    }
                                                  } else {
                                                    if (context.mounted) {
                                                      await snackBar(
                                                          context,
                                                          ErrorMessages
                                                              .offlineErrorTitle,
                                                          ErrorMessages
                                                              .offlineErrorMessage);
                                                    }
                                                  }
                                                })
                                              : null;
                                        },
                                        child: Container(
                                          width: MediaQuery.of(context)
                                                  .size
                                                  .width /
                                              1.15,
                                          margin: const EdgeInsets.only(
                                              top: 5, bottom: 15.0),
                                          padding: const EdgeInsets.only(
                                              top: 5.0,
                                              bottom: 5.0,
                                              left: 3,
                                              right: 0),
                                          decoration: BoxDecoration(
                                            color: selectedWard == poleWardName
                                                ? Theme.of(context).canvasColor
                                                : Theme.of(context)
                                                    .unselectedWidgetColor,
                                            borderRadius:
                                                BorderRadius.circular(5.0),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Theme.of(context)
                                                    .unselectedWidgetColor,
                                                offset: const Offset(3.0, 4.0),
                                                blurRadius: 6.0,
                                              ),
                                            ],
                                          ),
                                          child: Row(
                                            children: [
                                              ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(8.0),
                                                child: Image.file(
                                                    height: 42,
                                                    width: 42,
                                                    Assets.lamp),
                                              ),
                                              const SizedBox(width: 10),
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    if (brand.isNotEmpty)
                                                      _buildRichText(
                                                          "Brand:", " $brand"),
                                                    const SizedBox(height: 5),
                                                    Row(
                                                      children: [
                                                        if (wattage != 0)
                                                          _buildRichText(
                                                              "Wattage:",
                                                              " ${wattage}W"),
                                                        if (brand.isNotEmpty)
                                                          const SizedBox(
                                                              width: 14),
                                                        if (mfgYear.isNotEmpty)
                                                          _buildRichText(
                                                              "Mfg Year:",
                                                              " $mfgYear"),
                                                      ],
                                                    ),
                                                    const SizedBox(height: 5),
                                                    _buildRichText("Lamp ID:",
                                                        " $lampLabel"),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      )
                                    //container for add lamp
                                    : GestureDetector(
                                        onTap: () {
                                          selectedWard == poleWardName
                                              ? Utility.isConnected()
                                                  .then((value) async {
                                                  if (value) {
                                                    try {
                                                      bool
                                                          isLocationTrackingRequi =
                                                          await isLocationTrackingRequired();
                                                      if (isLocationTrackingRequi) {
                                                        final isLocationServiceOn =
                                                            await Utility
                                                                .ensureLocationServiceEnabled();
                                                        if (!isLocationServiceOn) {
                                                          return; //check if location is on
                                                        }
                                                      }

                                                      await ref
                                                          .read(
                                                              deviceController)
                                                          .clickEventIsFor(
                                                              "addLamp");
                                                      if (context.mounted) {
                                                        ref
                                                            .read(
                                                                deviceController)
                                                            .setLampLightPointId(
                                                                lampLightPointId);
                                                        await Navigator.of(
                                                                context)
                                                            .push(
                                                                MaterialPageRoute(
                                                          builder: (context) =>
                                                              ScanQRWithCustomScreen(
                                                                  firstOrSecond:
                                                                      1,
                                                                  clickEventIsFor:
                                                                      "addLamp"),
                                                        ));
                                                      }
                                                    } catch (e) {
                                                      log(e.toString());
                                                    }
                                                  } else {
                                                    if (context.mounted) {
                                                      await snackBar(
                                                          context,
                                                          ErrorMessages
                                                              .offlineErrorTitle,
                                                          ErrorMessages
                                                              .offlineErrorMessage);
                                                    }
                                                  }
                                                })
                                              : null;
                                        },
                                        child: Container(
                                          width: MediaQuery.of(context)
                                                  .size
                                                  .width /
                                              1.15,
                                          margin: const EdgeInsets.only(
                                              top: 4, bottom: 15.0),
                                          padding: const EdgeInsets.all(10),
                                          decoration: BoxDecoration(
                                              color: selectedWard ==
                                                      poleWardName
                                                  ? const Color.fromARGB(
                                                      248, 75, 129, 163)
                                                  : Theme.of(context)
                                                      .unselectedWidgetColor,
                                              borderRadius:
                                                  BorderRadius.circular(50.0),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Theme.of(context)
                                                      .unselectedWidgetColor,
                                                  offset:
                                                      const Offset(3.0, 4.0),
                                                  blurRadius: 6.0,
                                                ),
                                              ]),
                                          child: Row(
                                            children: [
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 10.0),
                                                child: Image.file(
                                                    height: 28,
                                                    width: 28,
                                                    Assets.lamp),
                                              ),
                                              const SizedBox(width: 10),
                                              Text(
                                                'Add Lamp',
                                                style: TextStyle(
                                                    fontSize: 15,
                                                    fontWeight: FontWeight.bold,
                                                    color: Theme.of(context)
                                                        .canvasColor),
                                              ),
                                              const Spacer(),
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                    right: 10.0),
                                                child: Icon(
                                                  Icons.qr_code_outlined,
                                                  size: 25,
                                                  color: Theme.of(context)
                                                      .canvasColor,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                if (ilmLabel.isNotEmpty)
                                  Text(
                                    "Installed ILM",
                                    style: TextStyle(
                                      fontSize: 13,
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ),
                                //container for already installed ilm
                                ilmLabel.isNotEmpty
                                    ? GestureDetector(
                                        onTap: () {
                                          selectedWard == poleWardName
                                              ? Utility.isConnected()
                                                  .then((value) async {
                                                  if (value) {
                                                    ref
                                                        .read(deviceController)
                                                        .userSelectedDeviceOrAsset(
                                                            ilmLabel);
                                                    if (context.mounted) {
                                                      await ref
                                                          .read(
                                                              deviceController)
                                                          .installedLampNavigation(
                                                              ref,
                                                              context,
                                                              ilmLabel,
                                                              1,
                                                              "1");
                                                    }
                                                  } else {
                                                    if (context.mounted) {
                                                      await snackBar(
                                                          context,
                                                          ErrorMessages
                                                              .offlineErrorTitle,
                                                          ErrorMessages
                                                              .offlineErrorMessage);
                                                    }
                                                  }
                                                })
                                              : null;
                                        },
                                        child: Container(
                                          width: MediaQuery.of(context)
                                                  .size
                                                  .width /
                                              1.0,
                                          padding: const EdgeInsets.all(5.0),
                                          margin:
                                              const EdgeInsets.only(bottom: 4),
                                          decoration: BoxDecoration(
                                            color: selectedWard == poleWardName
                                                ? Theme.of(context).canvasColor
                                                : Theme.of(context)
                                                    .unselectedWidgetColor,
                                            borderRadius:
                                                BorderRadius.circular(5.0),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Theme.of(context)
                                                    .unselectedWidgetColor,
                                                offset: const Offset(3.0, 4.0),
                                                blurRadius: 6.0,
                                              ),
                                            ],
                                          ),
                                          child: Row(
                                            children: [
                                              ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(8.0),
                                                child: Image.file(
                                                    height: 42,
                                                    width: 42,
                                                    Assets.ilmDeviceWithoutBg),
                                              ),
                                              const SizedBox(width: 10),
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    _buildRichText(
                                                        "ILM Installed:",
                                                        " Yes"),
                                                    const SizedBox(height: 5),
                                                    _buildRichText("ILM ID:",
                                                        " $ilmLabel"),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      )
                                    //container for add ilm
                                    : GestureDetector(
                                        onTap: () {
                                          selectedWard == poleWardName
                                              ? Utility.isConnected()
                                                  .then((value) async {
                                                  if (value) {
                                                    ref
                                                        .read(deviceController)
                                                        .setLampLightPointId(
                                                            lampLightPointId);
                                                    if (lampLabel.isNotEmpty) {
                                                      await ref
                                                          .read(
                                                              deviceController)
                                                          .clickEventIsFor(
                                                              "addILMViaPole");
                                                      if (context.mounted) {
                                                        await ref
                                                            .read(
                                                                deviceController)
                                                            .scanDevice(
                                                                ref,
                                                                context,
                                                                1,
                                                                "addILMViaPole");
                                                        if (context.mounted) {
                                                          await Navigator.of(
                                                                  context)
                                                              .push(
                                                                  MaterialPageRoute(
                                                            builder: (context) =>
                                                                ScanQRWithCustomScreen(
                                                                    firstOrSecond:
                                                                        1,
                                                                    clickEventIsFor:
                                                                        "addILMViaPole"),
                                                          ));
                                                        }
                                                      }
                                                    } else {
                                                      if (context.mounted) {
                                                        await addIlmWarningPopup(
                                                            ref,
                                                            context,
                                                            ErrorMessages
                                                                .lampIdGenerationWarning);
                                                      }
                                                    }
                                                  } else {
                                                    if (context.mounted) {
                                                      await snackBar(
                                                          context,
                                                          ErrorMessages
                                                              .offlineErrorTitle,
                                                          ErrorMessages
                                                              .offlineErrorMessage);
                                                    }
                                                  }
                                                })
                                              : null;
                                        },
                                        child: Container(
                                          width: MediaQuery.of(context)
                                                  .size
                                                  .width /
                                              1.15,
                                          margin:
                                              const EdgeInsets.only(bottom: 4),
                                          padding: const EdgeInsets.all(10),
                                          decoration: BoxDecoration(
                                              color: selectedWard ==
                                                      poleWardName
                                                  ? const Color.fromARGB(
                                                      248, 75, 129, 163)
                                                  : Theme.of(context)
                                                      .unselectedWidgetColor,
                                              borderRadius:
                                                  BorderRadius.circular(50.0),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Theme.of(context)
                                                      .unselectedWidgetColor,
                                                  offset:
                                                      const Offset(3.0, 4.0),
                                                  blurRadius: 6.0,
                                                ),
                                              ]),
                                          child: Row(
                                            children: [
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 10.0),
                                                child: Image.file(
                                                    height: 28,
                                                    width: 28,
                                                    Assets.ilmDeviceWithoutBg),
                                              ),
                                              const SizedBox(
                                                width: 10,
                                              ),
                                              Text(
                                                'Add ILM',
                                                style: TextStyle(
                                                    fontSize: 15,
                                                    fontWeight: FontWeight.bold,
                                                    color: Theme.of(context)
                                                        .canvasColor),
                                              ),
                                              const Spacer(),
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                    right: 10.0),
                                                child: Icon(
                                                  Icons.qr_code_outlined,
                                                  size: 25,
                                                  color: Theme.of(context)
                                                      .canvasColor,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                              ],
                            ),
                          );
                        }),
                      ),
                    ],
                  ),
                ],
              ),
            ]),
          ),
        ),
      ),
    );
  }
}

Widget _buildBubble(BuildContext context, String text) {
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 5.0, vertical: 5.0),
    decoration: BoxDecoration(
      color: Theme.of(context).dialogTheme.backgroundColor,
      borderRadius: BorderRadius.circular(8.0),
      border: Border.all(
          color: const Color.fromARGB(248, 75, 129, 163).withOpacity(0.5),
          width: 1),
    ),
    child: Text(
      text,
      style: TextStyle(
        fontSize: 10,
        fontWeight: FontWeight.bold,
        color: Theme.of(context).secondaryHeaderColor,
      ),
    ),
  );
}

Widget _buildRichText(String label, String value) {
  return RichText(
    text: TextSpan(
      children: [
        TextSpan(
          text: label,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 13,
            color: Colors.black,
          ),
        ),
        TextSpan(
          text: value,
          style: const TextStyle(
            fontWeight: FontWeight.normal,
            fontSize: 13,
            color: Colors.black,
          ),
        ),
      ],
    ),
  );
}
