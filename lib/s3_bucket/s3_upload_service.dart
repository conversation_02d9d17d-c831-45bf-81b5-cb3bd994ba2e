import 'dart:convert';
import 'dart:developer';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

import 's3_config.dart';

class S3UploadService {
  static final Dio _dio = Dio();

  Future<String> updateSurveyImageServiceS3(
      String base64Image, String fileName, BuildContext context,
      {bool isPPE = false}) async {
    try {
      log('Starting S3 upload for file: $fileName');

      final result = await uploadImageToS3(
        base64Image: base64Image,
        fileName: fileName,
        folder: isPPE ? S3Config.ppeImageFolder : S3Config.luminatorFolder,
        contentType: 'application/octet-stream',
      );

      log('S3 upload result: $result');

      // Check if upload was successful
      if (result != null && result.isNotEmpty) {
        log('S3 upload successful, URL: $result');
        return "200";
      } else {
        log('S3 upload failed - result is null or empty');
        return "400";
      }
    } catch (e) {
      log('S3 upload error: $e');
      log('S3 upload error type: ${e.runtimeType}');
      return 'Something Went Wrong';
    }
  }

  /// Upload image directly to S3 using presigned URL approach
  static Future<String?> uploadImageToS3({
    required String base64Image,
    required String fileName,
    required String folder,
    String contentType = 'application/octet-stream',
  }) async {
    try {
      log('Starting S3 upload for file: $fileName');

      // Convert base64 to bytes
      final bytes = base64Decode(base64Image);
      log('Base64 decoded successfully, bytes length: ${bytes.length}');

      if (bytes.isEmpty) {
        log('Error: Image bytes are empty');
        return null;
      }

      // Create the S3 key (path)
      final key = '$folder/$fileName';
      log('S3 Key: $key');

      // Upload using PUT request with AWS signature
      final result = await _uploadWithSignature(
        bytes: bytes,
        key: key,
        contentType: contentType,
      );

      if (result != null) {
        log('S3 upload successful, URL: $result');
        return result;
      } else {
        log('S3 upload failed');
        return null;
      }
    } catch (e) {
      log('S3 upload error: $e');
      return null;
    }
  }

  /// Upload using AWS Signature Version 4
  static Future<String?> _uploadWithSignature({
    required Uint8List bytes,
    required String key,
    required String contentType,
  }) async {
    try {
      final now = DateTime.now().toUtc();
      final dateStamp = _formatDate(now);
      final timeStamp = _formatDateTime(now);

      // Create the URL
      final url =
          'https://${S3Config.bucketName}.s3.${S3Config.region}.amazonaws.com/$key';

      // Create headers for signing (CRITICAL: Don't include Content-Type and Content-Length in signature)
      final headersForSigning = <String, String>{
        'Host': '${S3Config.bucketName}.s3.${S3Config.region}.amazonaws.com',
        'x-amz-date': timeStamp,
        'x-amz-content-sha256': _sha256Hash(bytes),
      };

      // Create authorization header
      final authHeader = _createAuthorizationHeader(
        method: 'PUT',
        uri: '/$key',
        headers: headersForSigning,
        dateStamp: dateStamp,
        timeStamp: timeStamp,
        payloadHash: _sha256Hash(bytes),
      );

      // Create final headers for the request (including Content-Type and Content-Length)
      final headers = <String, String>{
        ...headersForSigning,
        'Authorization': authHeader,
        'Content-Type': contentType,
        'Content-Length': bytes.length.toString(),
      };

      log('Uploading to URL: $url');
      log('Headers: $headers');

      // Debug: Print the exact request details
      log('=== REQUEST DEBUG ===');
      log('Method: PUT');
      log('URL: $url');
      log('Content-Type: $contentType');
      log('Content-Length: ${bytes.length}');
      log('Authorization: ${headers['Authorization']}');
      log('x-amz-date: ${headers['x-amz-date']}');
      log('x-amz-content-sha256: ${headers['x-amz-content-sha256']}');
      log('Host: ${headers['Host']}');
      log('==================');

      // Make the PUT request - CRITICAL: Don't set contentType in Options if it's in headers
      final response = await _dio.put(
        url,
        data: bytes,
        options: Options(
          headers: headers,
          responseType: ResponseType.plain,
          // Remove contentType from here since it's already in headers
        ),
      );

      if (response.statusCode == 200 || response.statusCode == 204) {
        return url;
      } else {
        log('Upload failed with status: ${response.statusCode}');
        log('Response: ${response.data}');
        return null;
      }
    } catch (e) {
      log('Upload with signature error: $e');
      if (e is DioError) {
        log('DioError details:');
        log('Status code: ${e.response?.statusCode}');
        log('Response data: ${e.response?.data}');
        log('Response headers: ${e.response?.headers}');
        log('Error type: ${e.type}');
        log('Error message: ${e.message}');
      }
      return null;
    }
  }

  /// Create AWS Signature Version 4 authorization header
  static String _createAuthorizationHeader({
    required String method,
    required String uri,
    required Map<String, String> headers,
    required String dateStamp,
    required String timeStamp,
    required String payloadHash,
  }) {
    // Step 1: Create canonical request
    final canonicalHeaders = headers.entries
        .map((e) => '${e.key.toLowerCase()}:${e.value.trim()}')
        .toList()
      ..sort();

    final signedHeaders = headers.keys.map((k) => k.toLowerCase()).toList()
      ..sort();

    final canonicalRequest = [
      method,
      uri,
      '', // query string
      '${canonicalHeaders.join('\n')}\n',
      signedHeaders.join(';'),
      payloadHash,
    ].join('\n');

    // Step 2: Create string to sign
    final credentialScope = '$dateStamp/${S3Config.region}/s3/aws4_request';
    final stringToSign = [
      'AWS4-HMAC-SHA256',
      timeStamp,
      credentialScope,
      _sha256Hash(utf8.encode(canonicalRequest)),
    ].join('\n');

    // Step 3: Calculate signature
    final signature = _calculateSignature(
      stringToSign: stringToSign,
      dateStamp: dateStamp,
    );

    // Step 4: Create authorization header
    return 'AWS4-HMAC-SHA256 '
        'Credential=${S3Config.accessKey}/$credentialScope, '
        'SignedHeaders=${signedHeaders.join(';')}, '
        'Signature=$signature';
  }

  /// Calculate AWS Signature Version 4 signature
  static String _calculateSignature({
    required String stringToSign,
    required String dateStamp,
  }) {
    final kDate =
        _hmacSha256(utf8.encode('AWS4${S3Config.secretKey}'), dateStamp);
    final kRegion = _hmacSha256(kDate, S3Config.region);
    final kService = _hmacSha256(kRegion, 's3');
    final kSigning = _hmacSha256(kService, 'aws4_request');
    final signature = _hmacSha256(kSigning, stringToSign);

    return signature.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
  }

  /// HMAC-SHA256 helper
  static List<int> _hmacSha256(List<int> key, String data) {
    final hmac = Hmac(sha256, key);
    return hmac.convert(utf8.encode(data)).bytes;
  }

  /// SHA256 hash helper
  static String _sha256Hash(List<int> data) {
    return sha256.convert(data).toString();
  }

  /// Format date for AWS (YYYYMMDD)
  static String _formatDate(DateTime date) {
    return '${date.year.toString().padLeft(4, '0')}'
        '${date.month.toString().padLeft(2, '0')}'
        '${date.day.toString().padLeft(2, '0')}';
  }

  /// Format datetime for AWS (YYYYMMDDTHHMMSSZ)
  static String _formatDateTime(DateTime date) {
    return '${_formatDate(date)}T'
        '${date.hour.toString().padLeft(2, '0')}'
        '${date.minute.toString().padLeft(2, '0')}'
        '${date.second.toString().padLeft(2, '0')}Z';
  }
}
