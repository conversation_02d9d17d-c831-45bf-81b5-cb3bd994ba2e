class S3Config {
  static const String bucketName = 'luminator-iotpro';
  static const String region = 'us-east-1';
  static const String accessKey = '********************';
  static const String secretKey = 'wLtFR5vFUs1q0xfpWs8JaqTPVefxhvNnPl9E04uR';
  static const String luminatorFolder = 'luminator';
  static const String ppeImageFolder = 'ppeImages';
  static const String grievanceFolder = 'grievances';

  // S3 endpoint URL
  static String get s3Endpoint =>
      'https://$bucketName.s3.$region.amazonaws.com';
}

// class S3Config {
//   static const String bucketName = 'schnell-s3-image';
//   static const String region = 'ap-south-1';
//   static const String accessKey = '********************';
//   static const String secretKey = 't99dd9UHF3h4OVvxbosN+JnJIsaD4jd/d+7JNIER';
//   static const String luminatorFolder = 'luminator';
//   static const String ppeImageFolder = 'ppeImages';
//   static const String grievanceFolder = 'grievances';

//   // S3 endpoint URL
//   static String get s3Endpoint =>
//       'https://$bucketName.s3.$region.amazonaws.com';
// }
