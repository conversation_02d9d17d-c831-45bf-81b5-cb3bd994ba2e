import 'package:flutter/material.dart';

class ItemCardWithCount extends StatelessWidget {
  const ItemCardWithCount({
    super.key,
    this.count = '',
    required this.title,
    required this.icon,
  });

  final String count;
  final String title;
  final Widget icon;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 3.0, right: 3.0),
      child: Card(
        elevation: 10,
        color: Theme.of(context).canvasColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(2.0),
        ),
        child: SizedBox(
          height: MediaQuery.of(context).size.height / 12.0,
          width: MediaQuery.of(context).size.width / 2.17,
          child: Padding(
            padding: const EdgeInsets.all(2.0),
            child: Row(children: [
              Container(
                  // color: Colors.amber,
                  padding: const EdgeInsets.only(left: 2, right: 2),
                  margin: const EdgeInsets.only(right: 15),
                  height: 42,
                  width: 42,
                  child: icon),
              Sized<PERSON>ox(
                width: MediaQuery.of(context).size.width / 4,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 3.0),
                      child: Text(
                        title,
                        style: TextStyle(
                          fontWeight: FontWeight.w900,
                          fontSize: 14,
                          color: Theme.of(context).cardColor,
                        ),
                      ),
                    ),
                    if (count.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Theme.of(context).canvasColor,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          count.toString(),
                          style: TextStyle(
                            fontSize: 12,
                            color: Theme.of(context).cardColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      )
                  ],
                ),
              )
            ]),
          ),
        ),
      ),
    );
  }
}
