import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'package:image/image.dart' as img;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:location/location.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';

import '../ticket_notification/ticket_model.dart';
import 'dialog_box.dart';
import 'error_messages.dart';

class Utility {
  static Future<bool> isConnected() async {
    var connectivityResult = await (Connectivity().checkConnectivity());
    log(connectivityResult.toString());
    if (connectivityResult.contains(ConnectivityResult.mobile)) {
      try {
        final result = await InternetAddress.lookup('iotpro.io');
        return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
      } on SocketException catch (_) {
        return false;
      }
    } else if (connectivityResult.contains(ConnectivityResult.wifi)) {
      try {
        final result = await InternetAddress.lookup('iotpro.io');
        return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
      } on SocketException catch (_) {
        return false;
      }
    }
    return false;
  }

  static Future<bool> ensureLocationServiceEnabled() async {
    final Location location = Location();
    final serviceEnabled = await location.serviceEnabled();

    if (!serviceEnabled) {
      return await location.requestService();
    }

    return true;
  }

  static Future<String> compressImage(String imagePath) async {
    final imageFile = File(imagePath);
    final originalBytes = await imageFile.readAsBytes();

    // Decode image
    img.Image? originalImage = img.decodeImage(originalBytes);
    if (originalImage == null) throw Exception("Failed to decode image");

    // Resize to fixed width (e.g. 1080) to reduce resolution
    final resizedImage =
        img.copyResize(originalImage, width: 1080); // maintain aspect ratio

    // Compress with fixed quality
    final compressedBytes = Uint8List.fromList(
      img.encodeJpg(resizedImage, quality: 70),
    );

    final sizeKB = compressedBytes.lengthInBytes / 1024;
    log("Compressed size: ${sizeKB.toStringAsFixed(2)} KB");

    return base64Encode(compressedBytes);
  }

//   static Future<String> compressImage(String imagePath) async {
//     final imageFile = File(imagePath);

//     final inputImageFile = ImageFile(
//       rawBytes: await imageFile.readAsBytes(),
//       filePath: imagePath,
//     );

//     final input = ImageFileConfiguration(
//       config: const Configuration(jpgQuality: 70), // reduce quality further
//       input: inputImageFile,
//     );

//     final compressedImage = compress(input); // ✅ await the compression

//     final base64Image = base64Encode(compressedImage.rawBytes);

//     log("Compressed size: ${compressedImage.rawBytes.lengthInBytes / 1024} KB");

//     return base64Image;
//   }
}

Future<Map<String, double>> getLocationData() async {
  Location location = Location();

  try {
    LocationData locationData = await location.getLocation();
    double latitude = locationData.latitude ?? 0.0;
    double longitude = locationData.longitude ?? 0.0;

    return {
      'latitude': latitude,
      'longitude': longitude,
    };
  } catch (e) {
    log('Error getting location: $e');
    return {
      'latitude': 0.0,
      'longitude': 0.0,
    };
  }
}

extension StringExtension on String {
  static String displayTimeAgoFromMilliseconds(String milliseconds) {
    final DateTime videoDate =
        DateTime.fromMillisecondsSinceEpoch(int.parse(milliseconds));
    final int diffInHours = DateTime.now().difference(videoDate).inHours;

    String timeAgo = '';
    String timeUnit = '';
    int timeValue = 0;

    if (diffInHours < 1) {
      final diffInMinutes = DateTime.now().difference(videoDate).inMinutes;
      timeValue = diffInMinutes;
      timeUnit = 'minute';
    } else if (diffInHours < 24) {
      timeValue = diffInHours;
      timeUnit = 'hour';
    } else {
      timeValue = (diffInHours / 24).floor();
      timeUnit = 'day';
    }
    // else if (diffInHours >= 24 * 7 && diffInHours < 24 * 30) {
    //   timeValue = (diffInHours / (24 * 7)).floor();
    //   timeUnit = 'week';
    // } else if (diffInHours >= 24 * 30 && diffInHours < 24 * 12 * 30) {
    //   timeValue = (diffInHours / (24 * 30)).floor();
    //   timeUnit = 'month';
    // } else {
    //   timeValue = (diffInHours / (24 * 365)).floor();
    //   timeUnit = 'year';
    // }

    timeAgo = '$timeValue $timeUnit';
    timeAgo += timeValue > 1 ? 's' : '';

    return '$timeAgo ago';
  }
}

Future<void> launchMap(BuildContext context, Ticket ticket) async {
  try {
    // List<Location> locations = await locationFromAddress(landmark);
    // double latitude = locations.first.latitude;
    // double longitude = locations.first.longitude;
    launchUrl(Uri.parse(
        "https://www.google.com/maps/search/?api=1&query=${ticket.latitude},${ticket.longitude}"));
  } catch (e) {
    if (context.mounted) {
      await snackBar(context, ErrorMessages.networkErrorTitle,
          ErrorMessages.networkErrorMessage);
    }
  }
}

Future<bool> requestNotificationPermissionUntilGranted(
    BuildContext context) async {
  var status = await Permission.notification.status;

  // If already granted, return immediately
  if (status.isGranted) {
    log("Notification permission already granted.");
    return true;
  }

  // First request
  status = await Permission.notification.request();

  if (status.isGranted) {
    log("Notification permission granted.");
    return true;
  } else if (status.isPermanentlyDenied) {
    // Show alert dialog before opening app settings
    bool shouldOpenSettings = await _showPermissionRequiredDialog(context);

    if (shouldOpenSettings) {
      await openAppSettings();

      // Wait for the user to return from settings
      // Note: This will detect app resuming but you might want a more robust solution
      // with AppLifecycleState for production apps
      await Future.delayed(const Duration(seconds: 2));

      // Check if permission was granted in settings
      status = await Permission.notification.status;

      if (status.isGranted) {
        return true;
      } else {
        return requestNotificationPermissionUntilGranted(context);
      }
    } else {
      return false;
    }
  } else {
    // Denied but not permanently
    log("Permission denied. Will request again...");
    // You might want to show a dialog explaining why the permission is needed
    await Future.delayed(const Duration(seconds: 1));
    return requestNotificationPermissionUntilGranted(context);
  }
}

Future<bool> _showPermissionRequiredDialog(BuildContext context) async {
  bool result = false;

  await showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return AlertDialog(
        title: const Text('Notification Permission Required'),
        content: const Text(
            'This app needs notification permission to function properly. '
            'Please grant notification permission in app settings.'),
        actions: <Widget>[
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              result = true;
            },
            child: const Text('Open Settings'),
          ),
        ],
      );
    },
  );

  return result;
}
