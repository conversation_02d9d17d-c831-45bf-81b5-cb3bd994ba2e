import 'dart:convert';
import 'dart:developer';
import 'package:shared_preferences/shared_preferences.dart';

Future<void> saveUser(String user, String userId, String userName) async {
  final prefs = await SharedPreferences.getInstance();
  prefs.setString('user', user);
  prefs.setString('userId', userId);
  prefs.setString('userName', userName);
}

Future<String> getUser() async {
  final prefs = await SharedPreferences.getInstance();
  return prefs.getString('user') ?? '';
}

Future<String> getUserId() async {
  final prefs = await SharedPreferences.getInstance();
  return prefs.getString('userId') ?? '';
}

Future<String> getUserName() async {
  final prefs = await SharedPreferences.getInstance();
  return prefs.getString('userName') ?? '';
}

Future<String> getUserToken() async {
  final prefs = await SharedPreferences.getInstance();
  return prefs.getString('token') ?? '';
}

Future<void> removeUser() async {
  final prefs = await SharedPreferences.getInstance();
  prefs.remove('user');
}

Future<bool> isLocationTrackingRequired() async {
  final prefs = await SharedPreferences.getInstance();
  return prefs.getBool('isLocationTrackingRequired') ?? false;
}

Future<bool> isPPERequired() async {
  final prefs = await SharedPreferences.getInstance();
  return prefs.getBool('isPPERequired') ?? false;
}

Future<void> storeLocationLocally(timestamp,
    {double? lat,
    double? long,
    bool locationEnabled = true,
    String? activityName,
    String? entityName,
    String? entityType,
    bool? isTicket}) async {
  final prefs = await SharedPreferences.getInstance();
  List<String> locations = prefs.getStringList("locations") ?? [];
  Map<String, dynamic> values = {
    if (!locationEnabled) ...{
      "locationEnabled": false,
    } else if (activityName == null) ...{
      // for periodic tracking
      "latitude": lat,
      "longitude": long,
      "activity": "Periodic Tracking",
      "locationEnabled": true,
    } else if (isTicket == true) ...{
      "latitude": lat,
      "longitude": long,
      "activity": activityName,
      "entityName": entityName,
      "entityType": entityType,
      "locationEnabled": true,
    } else ...{
      // Activity tracking
      "latitude": lat,
      "longitude": long,
      "activity": activityName,
      "entityName": entityName,
      "locationEnabled": true,
    }
  };
  locations.add(jsonEncode({"ts": timestamp, "values": values}));
  await prefs.setStringList("locations", locations);
  var storedLocations = prefs.getStringList("locations");
  log("Location saved locally $storedLocations");
}

Future<void> clearStoredLocations(location) async {
  final prefs = await SharedPreferences.getInstance();
  await prefs.remove(location);
  log("Stored locations cleared");
}

Future<void> storeLoginFlagsForUserTracking(
    shallCaptureAttendance,
    isLocationTrackingRequired,
    isPPERequired,
    locationTrackingInterval,
    employeeId) async {
  final prefs = await SharedPreferences.getInstance();
  await prefs.setBool('shallCaptureAttendance', shallCaptureAttendance);
  await prefs.setBool('isLocationTrackingRequired', isLocationTrackingRequired);
  await prefs.setBool('isPPERequired', isPPERequired);
  await prefs.setInt('locationTrackingInterval', locationTrackingInterval);
  await prefs.setString('employeeId', employeeId);
}
