import 'dart:io';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter_carousel_widget/flutter_carousel_widget.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_compression/image_compression.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:schnell_luminator/attendence_tracking.dart/user_tracking_controller.dart';
import 'package:schnell_luminator/device_page/device_controller.dart';
import 'package:schnell_luminator/device_page/gw/panel_actions_controller.dart';
import 'package:schnell_luminator/device_page/ilm/maintenance1.dart';
import 'package:schnell_luminator/device_page/ilm/scantest.dart';
import 'package:schnell_luminator/device_page/lamp/lamp_details_page.dart';
import 'package:schnell_luminator/location_selection/customer_list.dart';
import 'package:schnell_luminator/login_page/login_controller.dart';
import 'package:schnell_luminator/qr_scan_online.dart';
import 'package:schnell_luminator/ticket_notification/ticket_controller.dart';
import 'package:schnell_luminator/utils/constants.dart';
import 'package:another_flushbar/flushbar.dart';
import 'package:permission_handler/permission_handler.dart' as ph;
import 'package:schnell_luminator/utils/error_messages.dart';
import 'package:schnell_luminator/utils/utility.dart';
import 'package:lottie/lottie.dart';
import '../device_page/device_service.dart';
import '../device_page/gw/gw_maintenance1.dart';
import '../device_page/pole/pole_page.dart';
import '../device_page/take_photo.dart';
import '../home_page/dashboard.dart';
import '../location_selection/location_controller.dart';
import '../location_selection/region_list.dart';
import '../location_selection/ward_list.dart';
import '../location_selection/zone_list.dart';
import 'asset_folder.dart';
import 'session.dart';

final DeviceService _service = DeviceService();

warningPopup(WidgetRef ref, BuildContext context, String msg) async {
  QRViewController? qrViewController = ref
      .read(ref.watch(isSecondTime) ? qrController2 : qrController)
      .qrViewController;
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Lottie.file(Assets.userAlertLottie, height: 150, width: 150),
            const SizedBox(height: 20),
            Text(msg, textAlign: TextAlign.center),
          ],
        ),
        actions: <Widget>[
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextButton(
                onPressed: () {
                  Navigator.of(context, rootNavigator: true).pop();
                  if (qrViewController != null) {
                    qrViewController.resumeCamera();
                  }
                },
                child: Text("Back",
                    style: TextStyle(
                        color: Theme.of(context).unselectedWidgetColor)),
              ),
              const SizedBox(width: 50),
              OutlinedButton(
                style: OutlinedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 40),
                  side: BorderSide(color: Theme.of(context).cardColor),
                  backgroundColor: Theme.of(context).cardColor,
                ),
                onPressed: () {
                  Navigator.of(context, rootNavigator: true).pop();
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const LampDetailsPage()),
                  );
                },
                child: Text(
                  "Proceed",
                  style: TextStyle(color: Theme.of(context).canvasColor),
                ),
              ),
            ],
          ),
        ],
        backgroundColor: Theme.of(context).dialogTheme.backgroundColor,
      );
    },
  );
}

addIlmWarningPopup(WidgetRef ref, BuildContext context, String msg) async {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return PopScope(
        canPop: false,
        child: AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Lottie.file(
                Assets.userAlertLottie,
                height: 150,
                width: 150,
              ),
              const SizedBox(height: 20),
              Text(msg, textAlign: TextAlign.center),
            ],
          ),
          actions: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context, rootNavigator: true).pop();
                  },
                  child: Text("Cancel",
                      style: TextStyle(
                          color: Theme.of(context).unselectedWidgetColor)),
                ),
                const SizedBox(width: 40),
                OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20.0),
                    ),
                    side: BorderSide(color: Theme.of(context).cardColor),
                    padding: const EdgeInsets.symmetric(horizontal: 50),
                    backgroundColor: Theme.of(context).cardColor,
                  ),
                  onPressed: () async {
                    Navigator.of(context, rootNavigator: true).pop();
                    await ref
                        .read(deviceController)
                        .clickEventIsFor("addILMViaPole");
                    if (context.mounted) {
                      await Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) => ScanQRWithCustomScreen(
                            firstOrSecond: 1, clickEventIsFor: "addILMViaPole"),
                      ));
                    }
                  },
                  child: Text(
                    "OK",
                    style: TextStyle(color: Theme.of(context).canvasColor),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    },
  );
}

skipWarning(WidgetRef ref, BuildContext context, String msg,
    String clickEventIsFor) async {
  final cameras = await availableCameras();
  final firstCamera = cameras.first;
  String clickeventIsFor = ref.watch(deviceController).clickeventIsFor;
  QRViewController? qrViewController = ref
      .read(ref.watch(isSecondTime) ? qrController2 : qrController)
      .qrViewController;
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return PopScope(
        canPop: false,
        child: AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Lottie.file(Assets.userAlertLottie, height: 150, width: 150),
              const SizedBox(height: 20),
              Text(msg, textAlign: TextAlign.center),
            ],
          ),
          actions: <Widget>[
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              TextButton(
                onPressed: () {
                  Navigator.of(context, rootNavigator: true).pop();
                  if (qrViewController != null) {
                    qrViewController.resumeCamera();
                  }
                },
                child: Text("NO",
                    style: TextStyle(
                        color: Theme.of(context).unselectedWidgetColor)),
              ),
              const SizedBox(width: 50),
              OutlinedButton(
                style: OutlinedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20.0),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 50),
                    side: BorderSide(color: Theme.of(context).cardColor),
                    backgroundColor: Theme.of(context).cardColor),
                onPressed: () async {
                  Navigator.of(context, rootNavigator: true).pop();
                  if (clickEventIsFor == "lampOnlyReplace") {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TakePictureScreen(
                          camera: firstCamera,
                          clickeventIsFor: clickeventIsFor,
                        ),
                      ),
                    );
                  } else {
                    await ref
                        .read(deviceController)
                        .lampOrILMScanForReplace("ilm");
                    if (context.mounted) {
                      await Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => ScanQRWithCustomScreen(
                            firstOrSecond: 2,
                            clickEventIsFor: clickEventIsFor,
                          ),
                        ),
                      );
                    }
                  }
                },
                child: Text(
                  "YES",
                  style: TextStyle(color: Theme.of(context).canvasColor),
                ),
              ),
            ]),
          ],
        ),
      );
    },
  );
}

// skipWarning(WidgetRef ref, context, msg, clickEventIsFor) async {
//   final cameras = await availableCameras();
//   final firstCamera = cameras.first;
//   String clickeventIsFor = ref.watch(deviceController).clickeventIsFor;
//   QRViewController? qrViewController = ref.read(qrController).qrViewController;
//   await CoolAlert.show(
//       context: context,
//       barrierDismissible: false,
//       type: CoolAlertType.warning,
//       text: msg,
//       //textTextStyle: TextStyle(color: Theme.of(context).secondaryHeaderColor),
//       title: " ",
//       lottieAsset: 'assets/animation/userAlert.json',
//       confirmBtnText: "YES",
//       confirmBtnColor: Theme.of(context).cardColor,
//       onConfirmBtnTap: () async => {
//             Navigator.of(context, rootNavigator: true).pop(),
//             if (clickEventIsFor == "lampOnlyReplace")
//               {
//                 // if (qrViewController != null) {qrViewController.resumeCamera()},
//                 Navigator.push(
//                     context,
//                     MaterialPageRoute(
//                         builder: (context) => TakePictureScreen(
//                             camera: firstCamera,
//                             clickeventIsFor: clickeventIsFor)))
//               }
//             else
//               {
//                 await ref.read(deviceController).lampOrILMScanForReplace("ilm"),
//                 await Navigator.of(context).push(MaterialPageRoute(
//                   builder: (context) => ScanQRWithCustomScreen(
//                       firstOrSecond: 2, clickEventIsFor: clickEventIsFor),
//                 ))
//                 // await ref
//                 //     .read(deviceController)
//                 //     .scanDevice(ref, context, 2, clickEventIsFor),
//               }
//           },
//       showCancelBtn: true,
//       cancelBtnText: "NO",
//       onCancelBtnTap: () => {
//             Navigator.of(context, rootNavigator: true).pop(),
//             if (qrViewController != null) {qrViewController.resumeCamera()}
//           },
//       backgroundColor: Theme.of(context).dialogBackgroundColor);
// }

snackBar(context, title, msg) {
  Flushbar(
      backgroundColor: Theme.of(context).indicatorColor,
      title: title,
      message: msg,
      icon: Icon(Icons.cancel_outlined,
          size: 30, color: Theme.of(context).canvasColor),
      isDismissible: false,
      blockBackgroundInteraction: true,
      leftBarIndicatorColor: Theme.of(context).canvasColor,
      onTap: (flushbar) {
        flushbar.dismiss();
      }
      // duration: Duration(seconds: 20),
      ).show(context);
}

showLoadingdialog(context) => showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          backgroundColor: Colors.transparent,
          content: SizedBox(
            height: 100,
            width: 100,
            child: spinkit,
          ),
        );
      },
    );

warningAlert(ref, context) async {
  await QuickAlert.show(
      context: context,
      type: QuickAlertType.info,
      backgroundColor: Theme.of(context).dialogTheme.backgroundColor!,
      headerBackgroundColor: Theme.of(context).indicatorColor,
      title: 'Location is fetching',
      barrierDismissible: false,
      showCancelBtn: false,
      showConfirmBtn: false,
      text: 'Please Wait!',
      autoCloseDuration: const Duration(seconds: 5));
}
// AwesomeDialog(

//       context: context,
//       dialogType: DialogType.INFO,
//       animType: AnimType.SCALE,
//       title: 'Location is fetching',
//       desc: 'Please Wait!',
//       btnOkOnPress: () {},
//       btnOkColor: Theme.of(context).primaryColor,
//       dismissOnTouchOutside: false,
//       autoHide: const Duration(seconds: 3),
//     ).show();

wardRequiredAlert(BuildContext context, WidgetRef ref, String msg) {
  QRViewController? qrViewController = ref
      .read(ref.watch(isSecondTime) ? qrController2 : qrController)
      .qrViewController;
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return PopScope(
        canPop: false,
        child: AlertDialog(
          contentPadding: const EdgeInsets.all(0),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(4),
                      topRight: Radius.circular(4)),
                  color: Theme.of(context).unselectedWidgetColor,
                ),
                width: double.infinity,
                child: Lottie.file(
                  Assets.wardRequiredLottie,
                  height: 150,
                  width: 150,
                ),
              ),
              const SizedBox(height: 15),
              Text(
                'Error!!!',
                style: TextStyle(
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 15),
              Text(msg, textAlign: TextAlign.center),
              const SizedBox(height: 15),
            ],
          ),
          actions: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20.0),
                    ),
                    side: BorderSide(color: Theme.of(context).cardColor),
                    padding: const EdgeInsets.symmetric(horizontal: 50),
                    backgroundColor: Theme.of(context).cardColor,
                  ),
                  onPressed: () async {
                    Navigator.of(context, rootNavigator: true).pop();
                    if (qrViewController != null) {
                      qrViewController.resumeCamera();
                    }
                  },
                  child: Text(
                    "OK",
                    style: TextStyle(color: Theme.of(context).canvasColor),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    },
  );
}

// wardRequiredAlert(context, WidgetRef ref, String msg) {
//   QRViewController? qrViewController = ref.read(qrController).qrViewController;
//   CoolAlert.show(
//     context: context,
//     barrierDismissible: false,
//     type: CoolAlertType.error,
//     //closeOnConfirmBtnTap: false,
//     confirmBtnColor: Theme.of(context).primaryColor,
//     onConfirmBtnTap: () async {
//       Navigator.of(context, rootNavigator: true).pop();
//       if (qrViewController != null) {
//         qrViewController.resumeCamera();
//       }
//     },
//     //textTextStyle: TextStyle(color: Theme.of(context).secondaryHeaderColor),
//     text: msg,
//     lottieAsset: 'assets/animation/wardRequired.json',
//     backgroundColor: Theme.of(context).unselectedWidgetColor,
//   );
// }

Drawer customHomeDrawer(
  String usernmae,
  String usermail,
  BuildContext context,
  WidgetRef ref,
) {
  String selectedContextId = ref.watch(ticketController).selectedContextId;
  return Drawer(
    backgroundColor: Theme.of(context).dialogTheme.backgroundColor,
    child: ListView(
      children: <Widget>[
        UserAccountsDrawerHeader(
          decoration: BoxDecoration(
              color: Theme.of(context).cardColor.withOpacity(0.7)),
          accountName: Text(usernmae,
              style: TextStyle(
                fontSize: 18.0,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).canvasColor,
              )),
          accountEmail: Text(usermail,
              style: TextStyle(
                fontSize: 14.0,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).canvasColor,
              )),
          currentAccountPicture: GestureDetector(
            child: CircleAvatar(
              backgroundColor: Theme.of(context).dialogTheme.backgroundColor,
              child: Icon(Icons.person,
                  color: Theme.of(context).textTheme.bodySmall!.color),
            ),
          ),
        ),
        InkWell(
            onTap: () {
              Utility.isConnected().then((value) async {
                if (value) {
                  bool isLocationTrackingRequi =
                      await isLocationTrackingRequired();
                  if (isLocationTrackingRequi) {
                    //auto logout after 24 hrs
                    bool didAutoLogout = await ref
                        .read(userTrackingController)
                        .autoLogout(context, ref);
                    if (didAutoLogout) return;
                  }
                  if (context.mounted) {
                    Navigator.pop(context);
                  }
                  ref.read(bottomMenuStateProvider.state).state = 0;
                  if (context.mounted) {
                    await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => HomePage(),
                      ),
                    );
                  }
                } else {
                  if (context.mounted) {
                    await snackBar(context, ErrorMessages.offlineErrorTitle,
                        ErrorMessages.offlineErrorMessage);
                  }
                }
              });
            },
            child: ListTile(
              title: Text('Home',
                  style: TextStyle(
                      fontSize: 14.0,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).cardColor)),
              leading: Icon(Icons.home, color: Theme.of(context).cardColor),
            )),
        InkWell(
            onTap: () {
              Utility.isConnected().then((value) async {
                if (value) {
                  EasyLoading.show(
                    status: '',
                    dismissOnTap: false,
                  );
                  bool isLocationTrackingRequi =
                      await isLocationTrackingRequired();
                  if (isLocationTrackingRequi) {
                    //auto logout after 24 hrs
                    bool didAutoLogout = await ref
                        .read(userTrackingController)
                        .autoLogout(context, ref);
                    if (didAutoLogout) return;
                  }
                  // if (selectedWard != '') {
                  if (context.mounted) {
                    Navigator.pop(context);
                  }
                  await ref.read(locationController).fetchCurrentLocation();
                  if (context.mounted) {
                    await ref
                        .read(deviceController)
                        .getLatLongBasedonWard(ref, context, selectedContextId);
                  }
                  EasyLoading.dismiss();
                  ref.read(bottomMenuStateProvider.state).state = 1;

                  if (context.mounted) {
                    await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => HomePage(),
                      ),
                    );
                  }
                  // } else {
                  //   wardRequiredAlert(
                  //       context, ref, ErrorMessages.wardRequiredAlertMessage);
                  // }
                } else {
                  if (context.mounted) {
                    await snackBar(context, ErrorMessages.offlineErrorTitle,
                        ErrorMessages.offlineErrorMessage);
                  }
                }
              });
            },
            child: ListTile(
              title: Text('Map View',
                  style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).cardColor,
                  )),
              leading: Icon(
                Icons.my_location_outlined,
                color: Theme.of(context).cardColor,
              ),
            )),
        InkWell(
            onTap: () {
              Utility.isConnected().then((value) async {
                if (value) {
                  bool isLocationTrackingRequi =
                      await isLocationTrackingRequired();
                  if (isLocationTrackingRequi) {
                    //auto logout after 24 hrs
                    bool didAutoLogout = await ref
                        .read(userTrackingController)
                        .autoLogout(context, ref);
                    if (didAutoLogout) return;
                  }
                  // if (selectedWard != '') {
                  if (context.mounted) {
                    Navigator.pop(context);
                  }
                  if (context.mounted) {
                    Navigator.of(context).push(MaterialPageRoute(
                      builder: (context) => ScanQRWithCustomScreen(
                          firstOrSecond: 1, clickEventIsFor: "1"),
                    ));
                  }
                  // } else {
                  //   wardRequiredAlert(
                  //       context, ref, ErrorMessages.wardRequiredAlertMessage);
                  // }
                } else {
                  if (context.mounted) {
                    await snackBar(context, ErrorMessages.offlineErrorTitle,
                        ErrorMessages.offlineErrorMessage);
                  }
                }
              });
            },
            child: ListTile(
              title: Text('Scan QR',
                  style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).cardColor,
                  )),
              leading: Icon(
                Icons.qr_code,
                color: Theme.of(context).cardColor,
              ),
            )),
        InkWell(
            onTap: () {
              Utility.isConnected().then((value) async {
                if (value) {
                  ref.read(bottomMenuStateProvider.state).state = 0;
                  if (context.mounted) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CustomerList(),
                      ),
                    );
                  }
                } else {
                  if (context.mounted) {
                    await snackBar(context, ErrorMessages.offlineErrorTitle,
                        ErrorMessages.offlineErrorMessage);
                  }
                }
              });
            },
            child: ListTile(
              title: Text('Change Customer',
                  style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).cardColor,
                  )),
              leading: Icon(
                Icons.location_on_outlined,
                color: Theme.of(context).cardColor,
              ),
            )),
        const SizedBox(
          height: 150,
        ),
        Divider(color: Theme.of(context).hoverColor),
        InkWell(
            onTap: () {
              Navigator.pop(context);
              logoutPop(context, ref);
            },
            child: ListTile(
              title: Text('Logout',
                  style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).cardColor,
                  )),
              leading: Icon(
                Icons.logout_outlined,
                color: Theme.of(context).cardColor,
              ),
            )),
        const SizedBox(
          height: 130,
        ),
        Padding(
          padding: const EdgeInsets.only(left: 110),
          child: Text(
            baseURL.contains('iotpro') ? '$appVersion - Beta' : appVersion,
            style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.bold,
                fontSize: 14),
          ),
        ),
      ],
    ),
  );
}

class SelectedLocationData extends ConsumerWidget {
  const SelectedLocationData({
    super.key,
    required this.selectedCustomer,
    required this.selectedRegion,
    required this.selectedZone,
    required this.selectedWard,
  });
  final String selectedCustomer;
  final String selectedRegion;
  final String selectedZone;
  final String selectedWard;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.1,
      child: Column(
        children: [
          GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CustomerList(),
                ),
              );
            },
            child: Padding(
              padding: const EdgeInsets.only(
                  top: 0.0, bottom: 5.0, left: 4, right: 4),
              child: Container(
                height: 35,
                width: MediaQuery.of(context).size.width / 1,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: Theme.of(context).primaryColor.withOpacity(0.5),
                    border: Border.all(
                        width: 2,
                        color:
                            Theme.of(context).primaryColor.withOpacity(0.8))),
                child: Center(
                  child: AutoSizeText(
                    selectedCustomer,
                    style: const TextStyle(
                        fontSize: 12, fontWeight: FontWeight.bold),
                    maxLines: 1,
                  ),
                ),
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GestureDetector(
                onTap: () {
                  showRegionDialog(context, ref);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 5.0),
                  height: 35,
                  width: MediaQuery.of(context).size.width / 3.2,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Theme.of(context).primaryColor.withOpacity(0.5),
                      border: Border.all(
                          width: 2,
                          color:
                              Theme.of(context).primaryColor.withOpacity(0.8))),
                  child: Center(
                    child: AutoSizeText(
                      selectedRegion == '' ? 'All Regions' : selectedRegion,
                      style: const TextStyle(
                          fontSize: 12, fontWeight: FontWeight.bold),
                      maxLines: 1,
                    ),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  if (selectedRegion != '') {
                    showZoneSelectDialog(context, ref);
                  }
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 5.0),
                  height: 35,
                  width: MediaQuery.of(context).size.width / 3.2,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: selectedRegion != ''
                          ? Theme.of(context).primaryColor.withOpacity(0.5)
                          : Theme.of(context).primaryColor.withOpacity(0.3),
                      border: Border.all(
                          width: 2,
                          color: selectedRegion != ''
                              ? Theme.of(context).primaryColor.withOpacity(0.8)
                              : Theme.of(context)
                                  .dialogTheme
                                  .backgroundColor!
                                  .withOpacity(0.8))
                      // color: Theme.of(context).primaryColor.withOpacity(0.5),
                      // border: Border.all(
                      //     width: 2,
                      //     color:
                      //         Theme.of(context).primaryColor.withOpacity(0.8))
                      ),
                  child: Center(
                    child: AutoSizeText(
                      selectedZone == '' ? 'All Zones' : selectedZone,
                      style: const TextStyle(
                          fontSize: 12, fontWeight: FontWeight.bold),
                      maxLines: 1,
                    ),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  if (selectedZone != '') {
                    showWardSelectDialog(context, ref);
                  }
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 5.0),
                  height: 35,
                  width: MediaQuery.of(context).size.width / 3.2,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: selectedZone != ''
                          ? Theme.of(context).primaryColor.withOpacity(0.5)
                          : Theme.of(context).primaryColor.withOpacity(0.3),
                      border: Border.all(
                          width: 2,
                          color: selectedZone != ''
                              ? Theme.of(context).primaryColor.withOpacity(0.8)
                              : Theme.of(context)
                                  .dialogTheme
                                  .backgroundColor!
                                  .withOpacity(0.8))),
                  child: Center(
                    child: AutoSizeText(
                      selectedWard == '' ? 'All Wards' : selectedWard,
                      style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: selectedZone != ''
                              ? Theme.of(context).secondaryHeaderColor
                              : Theme.of(context).cardColor),
                      maxLines: 1,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

Widget selectedContextContainer(
    BuildContext context, WidgetRef ref, locationContext) {
  return Container(
    margin: const EdgeInsets.only(
      left: 2,
      right: 22,
    ),
    height: 30,
    // width:
    //     MediaQuery.of(context).size.width / 2.4,
    decoration: BoxDecoration(
        color: Theme.of(context).canvasColor,
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.8),
        ),
        borderRadius: BorderRadius.circular(8)),
    child: Center(
      child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
        Flexible(
          child: FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              locationContext,
              style: TextStyle(
                  color: Theme.of(context).secondaryHeaderColor,
                  fontSize: 12,
                  fontWeight: FontWeight.bold),
            ),
          ),
        )
      ]),
    ),
  );
}

Widget maintenanceActivityButton(BuildContext context,
    {VoidCallback? onTap,
    required String text,
    Color? bgcolor,
    Color? iccolor,
    required File? image}) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 8.0),
    child: SizedBox(
      width: 145,
      child: Material(
        elevation: 5,
        color: bgcolor,
        borderRadius: BorderRadius.circular(10.0),
        child: MaterialButton(
          padding: const EdgeInsets.only(left: 10),
          height: 60,
          onPressed: onTap,
          child: Stack(alignment: Alignment.centerLeft, children: [
            Text(
              text,
              style:
                  TextStyle(color: Theme.of(context).canvasColor, fontSize: 16),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Padding(
                  padding: const EdgeInsets.only(right: 2.0),
                  child: Container(
                    decoration: BoxDecoration(
                        color: iccolor,
                        shape: BoxShape.rectangle,
                        borderRadius: BorderRadius.circular(10)),
                    child: Padding(
                      padding: const EdgeInsets.all(5.0),
                      child: Image.file(
                        image!,
                        height: 42,
                        width: 42,
                      ),
                    ),
                  ),
                )
              ],
            )
          ]),
        ),
      ),
    ),
  );
}

informationAlert(WidgetRef ref, BuildContext context, String msg) {
  QRViewController? qrViewController = ref
      .read(ref.watch(isSecondTime) ? qrController2 : qrController)
      .qrViewController;
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return PopScope(
        canPop: false,
        child: AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Lottie.file(
                Assets.userAlertLottie,
                height: 150,
                width: 150,
              ),
              const SizedBox(height: 15),
              Text(msg, textAlign: TextAlign.center),
            ],
          ),
          actions: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20.0),
                    ),
                    side: BorderSide(color: Theme.of(context).cardColor),
                    padding: const EdgeInsets.symmetric(horizontal: 40),
                    backgroundColor: Theme.of(context).cardColor,
                  ),
                  onPressed: () {
                    Navigator.of(context, rootNavigator: true).pop();
                    if (qrViewController != null) {
                      qrViewController.resumeCamera();
                    }
                  },
                  child: Text(
                    "OK",
                    style: TextStyle(color: Theme.of(context).canvasColor),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    },
  );
}

// informationAlert(WidgetRef ref, context, msg) {
//   QRViewController? qrViewController = ref.read(qrController).qrViewController;
//   CoolAlert.show(
//     context: context,
//     barrierDismissible: false,
//     //closeOnConfirmBtnTap: false,
//     type: CoolAlertType.info,
//     title: '',
//     text: msg,
//     //textTextStyle: TextStyle(color: Theme.of(context).secondaryHeaderColor),
//     confirmBtnColor: Theme.of(context).cardColor,
//     backgroundColor: Theme.of(context).dialogBackgroundColor,
//     lottieAsset: 'assets/animation/userAlert.json',
//     onConfirmBtnTap: () => {
//       Navigator.of(context, rootNavigator: true).pop(),
//       if (qrViewController != null) {qrViewController.resumeCamera()},
//     },
//   );
// }

void showSnackBar(WidgetRef ref, BuildContext context, String msg) {
  QRViewController? qrViewController = ref
      .read(ref.watch(isSecondTime) ? qrController2 : qrController)
      .qrViewController;
  SnackBar snackBar = SnackBar(
    // dismissDirection: DismissDirection.horizontal,
    duration: const Duration(seconds: 4),
    content: Text(msg),
    backgroundColor: Theme.of(context).indicatorColor,
    behavior: SnackBarBehavior.floating,
  );
  // ScaffoldMessenger.of(context).showSnackBar(snackBar);
  ScaffoldMessenger.of(context).showSnackBar(snackBar).closed.then((reason) {
    if (qrViewController != null) {
      qrViewController.resumeCamera();
    }
  });
}

// successTick(context, msg) {}
successTick(context, msg) async {
  await QuickAlert.show(
      context: context,
      type: QuickAlertType.success,
      backgroundColor: Theme.of(context).dialogTheme.backgroundColor!,
      headerBackgroundColor: Theme.of(context).indicatorColor,
      title: '',
      barrierDismissible: false,
      showCancelBtn: false,
      showConfirmBtn: false,
      text: msg,
      autoCloseDuration: const Duration(seconds: 3));
}
// AwesomeDialog(
//   context: context,
//   dialogType: DialogType.SUCCES,
//   animType: AnimType.SCALE,
//   title: 'Success',
//   desc: msg,
//   btnOkOnPress: () {},
//   btnOkColor: Theme.of(context).primaryColor,
//   dismissOnTouchOutside: false,
//   autoHide: const Duration(seconds: 3),
// ).show();

// successTick(BuildContext context, String msg) {
//   showDialog(
//     context: context,
//     barrierDismissible: false,
//     builder: (BuildContext context) {
//       // Future.delayed(const Duration(seconds: 10), () {
//       //   Navigator.of(context).pop(); // Auto dismiss after 3 seconds
//       // });
//       return Dialog(
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(15.0),
//         ),
//         child: Container(
//           padding: const EdgeInsets.all(20.0),
//           width: 500,
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               Lottie.file(
//                 Assets.successtickLottie,
//                 height: 100,
//               ),
//               const SizedBox(height: 20),
//               Text(
//                 msg,
//                 textAlign: TextAlign.center,
//                 style: const TextStyle(fontSize: 14),
//               ),
//             ],
//           ),
//         ),
//       );
//     },
//   );
// }
// lampInstalltionDiffWard(context, WidgetRef ref, msg, clickEventIsFor) =>
//     CoolAlert.show(
//       context: context,
//       type: CoolAlertType.loading,
//       showCancelBtn: false,

//       width: 500,
//       // title: msg,
//       barrierDismissible: false,
//       text: msg,
//       // textTextStyle: TextStyle(color: Theme.of(context).secondaryHeaderColor),
//       lottieAsset: 'assets/animation/userAlert.json',
//       widget: WillPopScope(
//         onWillPop: () async {
//           // Return false to prevent the back button from closing the dialog
//           return false;
//         },
//         child: Column(
//           children: [
//             _buildButton(
//               context,
//               onTap: () async {
//                 await Navigator.of(context).push(
//                   MaterialPageRoute(
//                     builder: (context) => const IlmMaintenance(),
//                   ),
//                 );
//               },
//               text: "TAKE TO MAINTENANCE",
//               bgcolor: Theme.of(context).hintColor,
//               iccolor: Colors.transparent.withOpacity(0.3),
//               icon: Icons.check_sharp,
//             ),
//             _buildButton(
//               context,
//               onTap: () async {
//                 Navigator.of(context, rootNavigator: true).pop();
//               },
//               text: "SKIP",
//               bgcolor: darkYellow,
//               iccolor: Colors.transparent.withOpacity(0.3),
//               icon: Icons.settings_backup_restore_rounded,
//             ),
//           ],
//         ),
//       ),
//     );

installationConfirmwithDiffCust(context, WidgetRef ref, msg, clickEventIsFor) {
  QRViewController? qrViewController = ref
      .read(ref.watch(isSecondTime) ? qrController2 : qrController)
      .qrViewController;
  String clickeventIsFor = ref.watch(deviceController).clickeventIsFor;

  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return WillPopScope(
        onWillPop: () async {
          return false;
        },
        child: AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 120.0,
                height: 135.0,
                child: Lottie.file(
                  Assets.userAlertLottie,
                  fit: BoxFit.fill,
                ),
              ),
              const SizedBox(height: 10),
              Text(
                msg,
                style: TextStyle(color: Theme.of(context).secondaryHeaderColor),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),
              if (clickEventIsFor == "addILM" ||
                  clickEventIsFor == "addILMViaPole")
                _buildButton(
                  context,
                  onTap: () async {
                    final cameras = await availableCameras();
                    final firstCamera = cameras.first;
                    if (context.mounted) {
                      Navigator.of(context, rootNavigator: true).pop();
                    }
                    if (context.mounted) {
                      await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => TakePictureScreen(
                            camera: firstCamera,
                            clickeventIsFor: clickeventIsFor,
                          ),
                        ),
                      );
                    }
                  },
                  text: "PROCEED TO INSTALL",
                  bgcolor: Theme.of(context).hintColor,
                  iccolor: Colors.transparent.withOpacity(0.3),
                  icon: Icons.check_sharp,
                ),
              _buildButton(
                context,
                onTap: () async {
                  Navigator.of(context, rootNavigator: true).pop();
                  if (qrViewController != null) {
                    qrViewController.resumeCamera();
                  }
                },
                text: "TRY ANOTHER DEVICE/ASSET",
                bgcolor: darkYellow,
                iccolor: Colors.transparent.withOpacity(0.3),
                icon: Icons.settings_backup_restore_rounded,
              ),
            ],
          ),
        ),
      );
    },
  );
}

installationConfirmwithDiffCustForPole(context, WidgetRef ref, msg) {
  QRViewController? qrViewController = ref
      .read(ref.watch(isSecondTime) ? qrController2 : qrController)
      .qrViewController;
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return WillPopScope(
        onWillPop: () async {
          return false;
        },
        child: AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 120.0,
                height: 135.0,
                child: Lottie.file(
                  Assets.userAlertLottie,
                  fit: BoxFit.fill,
                ),
              ),
              const SizedBox(height: 10),
              Text(
                msg,
                style: TextStyle(color: Theme.of(context).secondaryHeaderColor),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),
              _buildButton(
                context,
                onTap: () async {
                  Navigator.of(context, rootNavigator: true).pop();
                  if (qrViewController != null) {
                    qrViewController.resumeCamera();
                  }
                },
                text: "TRY ANOTHER DEVICE/ASSET",
                bgcolor: darkYellow,
                iccolor: Colors.transparent.withOpacity(0.3),
                icon: Icons.settings_backup_restore_rounded,
              ),
            ],
          ),
        ),
      );
    },
  );
}

installationConfirmForPole(context, WidgetRef ref, msg, navigationPath) {
  QRViewController? qrViewController = ref
      .read(ref.watch(isSecondTime) ? qrController2 : qrController)
      .qrViewController;
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return WillPopScope(
        onWillPop: () async {
          return false;
        },
        child: AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 120.0,
                height: 135.0,
                child: Lottie.file(
                  Assets.userAlertLottie,
                  fit: BoxFit.fill,
                ),
              ),
              const SizedBox(height: 10),
              Text(
                msg,
                style: TextStyle(color: Theme.of(context).secondaryHeaderColor),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),
              _buildButton(
                context,
                onTap: () async {
                  Navigator.of(context, rootNavigator: true).pop();
                  if (qrViewController != null) {
                    qrViewController.resumeCamera();
                  }
                },
                text: "TRY ANOTHER DEVICE/ASSET",
                bgcolor: darkYellow,
                iccolor: Colors.transparent.withOpacity(0.3),
                icon: Icons.settings_backup_restore_rounded,
              ),
              _buildButton(
                context,
                onTap: () async {
                  Navigator.of(context, rootNavigator: true).pop();
                  await Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) =>
                            PoleDetails(navigationPath: navigationPath)),
                  );
                },
                text: "TAKE TO MAINTENANCE",
                bgcolor: Theme.of(context).primaryColor,
                iccolor: Theme.of(context).cardColor,
                icon: Icons.construction,
              ),
            ],
          ),
        ),
      );
    },
  );
}

installationConfirm(context, WidgetRef ref, msg, deviceType, clickEventIsFor) {
  QRViewController? qrViewController = ref
      .read(ref.watch(isSecondTime) ? qrController2 : qrController)
      .qrViewController;
  String clickEventIsFor = ref.watch(deviceController).clickeventIsFor;
  String hubOrCcms = ref.watch(deviceController).hubOrCcms;

  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return WillPopScope(
        onWillPop: () async {
          return false;
        },
        child: AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 120.0,
                height: 135.0,
                child: Lottie.file(
                  Assets.userAlertLottie,
                  fit: BoxFit.fill,
                ),
              ),
              const SizedBox(height: 10),
              Text(
                msg,
                style: TextStyle(color: Theme.of(context).secondaryHeaderColor),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),
              if (clickEventIsFor == "addILM" ||
                  clickEventIsFor == "addILMViaPole")
                _buildButton(
                  context,
                  onTap: () async {
                    final cameras = await availableCameras();
                    final firstCamera = cameras.first;
                    if (context.mounted) {
                      Navigator.of(context, rootNavigator: true).pop();
                    }
                    if (context.mounted) {
                      await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => TakePictureScreen(
                            camera: firstCamera,
                            clickeventIsFor: clickEventIsFor,
                          ),
                        ),
                      );
                    }
                  },
                  text: "PROCEED TO INSTALL",
                  bgcolor: Theme.of(context).hintColor,
                  iccolor: Colors.transparent.withOpacity(0.3),
                  icon: Icons.check_sharp,
                ),
              _buildButton(
                context,
                onTap: () async {
                  Navigator.of(context, rootNavigator: true).pop();
                  if (qrViewController != null) {
                    qrViewController.resumeCamera();
                  }
                },
                text: "TRY ANOTHER DEVICE/ASSET",
                bgcolor: darkYellow,
                iccolor: Colors.transparent.withOpacity(0.3),
                icon: Icons.settings_backup_restore_rounded,
              ),
              _buildButton(
                context,
                onTap: () async {
                  Navigator.of(context, rootNavigator: true).pop();
                  if (deviceType == "GW") {
                    if (hubOrCcms == 'ccms') {
                      await ref
                          .read(panelController)
                          .commRelayStatusCalculation(ref, context);
                    }
                    if (context.mounted) {
                      await Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => GwMaintenance(),
                        ),
                      );
                    }
                  } else {
                    await Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const IlmMaintenance(),
                      ),
                    );
                  }
                },
                text: "TAKE TO MAINTENANCE",
                bgcolor: Theme.of(context).primaryColor,
                iccolor: Theme.of(context).cardColor,
                icon: Icons.construction,
              ),
            ],
          ),
        ),
      );
    },
  );
}

Widget _buildButton(BuildContext context,
    {VoidCallback? onTap,
    required String text,
    Color? bgcolor,
    Color? iccolor,
    required IconData? icon}) {
  return Padding(
    padding:
        const EdgeInsets.only(left: 10.0, right: 10.0, top: 10.0, bottom: 10.0),
    child: Material(
      elevation: 5,
      color: bgcolor,
      borderRadius: BorderRadius.circular(15.0),
      child: MaterialButton(
        height: 55,
        onPressed: onTap,
        child: Stack(
          alignment: Alignment.centerLeft,
          children: [
            Text(
              text,
              style:
                  TextStyle(color: Theme.of(context).canvasColor, fontSize: 10),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  padding: const EdgeInsets.all(7),
                  decoration:
                      BoxDecoration(color: iccolor, shape: BoxShape.circle),
                  child: Icon(
                    icon,
                    color: Theme.of(context).canvasColor,
                  ),
                )
              ],
            )
          ],
        ),
      ),
    ),
  );
}

logoutPop(BuildContext context, WidgetRef ref) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Lottie.file(
              Assets.logoutLottie,
              height: 150,
              width: 150,
            ),
            const SizedBox(height: 15),
            const Text(
              'Are you sure you want to Logout?',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context, rootNavigator: true).pop();
                  },
                  child: Text("Cancel",
                      style: TextStyle(
                          color: Theme.of(context).unselectedWidgetColor)),
                ),
                OutlinedButton(
                  style: OutlinedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20.0),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 40),
                      side: BorderSide(color: Theme.of(context).indicatorColor),
                      backgroundColor: Theme.of(context).indicatorColor),
                  onPressed: () async {
                    ref.read(deviceController).emptyLocation();
                    ref.read(locationController).emptyCustomer();
                    removeUser();
                    ref.read(loginController).logoutLoadfalse();
                    ref.read(loginController).textFieldUpdate('', true);
                    ref.read(loginController).textFieldUpdate('', false);
                    await ref.read(loginController).logout(context, ref);
                  },
                  child: Text(
                    "Logout",
                    style: TextStyle(color: Theme.of(context).canvasColor),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    },
  );
}

// logoutPop(context, WidgetRef ref) => CoolAlert.show(
//     barrierDismissible: false,
//     context: context,
//     type: CoolAlertType.confirm,
//     // closeOnConfirmBtnTap: false,
//     title: "",
//     text: 'Are you sure you want to Logout?',
//     // textTextStyle: TextStyle(color: Theme.of(context).secondaryHeaderColor),
//     lottieAsset: 'assets/animation/logout.json',
//     onConfirmBtnTap: () => {
//           ref.read(deviceController).emptyLocation(), //updatednow
//           ref.read(locationController).emptyCustomer(),
//           removeUser(),
//           ref.read(loginController).logoutLoadfalse(),
//           ref.read(loginController).textFieldUpdate('', true),
//           ref.read(loginController).textFieldUpdate('', false),
//           Navigator.of(context)
//               .pushNamedAndRemoveUntil(loginRoute, (route) => false),
//         },
//     onCancelBtnTap: () => {Navigator.of(context, rootNavigator: true).pop()},
//     confirmBtnColor: Theme.of(context).indicatorColor,
//     backgroundColor: Theme.of(context).canvasColor);

showDialogFunc(
    context, ref, data, deviceImage, lamp, clickEventIsFor, poleName) {
  File imageFile = File(deviceImage);
  ImageFile inputImageFile = ImageFile(
    rawBytes: imageFile.readAsBytesSync(),
    filePath: deviceImage,
  );

  return showDialog(
    barrierDismissible: false,
    context: context,
    builder: (context) {
      return WillPopScope(
        onWillPop: () async => false,
        child: Center(
          child: Material(
            type: MaterialType.transparency,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Theme.of(context).canvasColor,
              ),
              padding: const EdgeInsets.all(15),
              height: MediaQuery.of(context).size.height * 0.76,
              width: MediaQuery.of(context).size.width * 0.88,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  Text(
                    "Device successfully installed!",
                    style: TextStyle(
                        color: Theme.of(context).hintColor,
                        fontSize: 20,
                        fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(5),
                    child: Container(
                      decoration: BoxDecoration(
                          color: Theme.of(context).canvasColor,
                          border: Border.all(
                              width: 10,
                              color: Theme.of(context)
                                  .dialogTheme
                                  .backgroundColor!),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(20))),
                      child: SizedBox(
                        // width: MediaQuery.of(context).size.width * 0.7,
                        height: MediaQuery.of(context).size.height / 2,
                        child: Image.memory(inputImageFile.rawBytes),
                      ),
                    ),
                  ),
                  // const SizedBox(
                  //   height: 20,
                  // ),
                  // Padding(
                  //   padding: const EdgeInsets.symmetric(horizontal: 50.0),
                  //   child: Column(
                  //     crossAxisAlignment: CrossAxisAlignment.start,
                  //     mainAxisSize: MainAxisSize.min,
                  //     children: <Widget>[
                  //       const SizedBox(
                  //         height: 18,
                  //       ),
                  // lineDesign(context, 'Device', deviceName),
                  // infoDivider(context),
                  // lineDesign(context, 'Accuracy', result),
                  // infoDivider(context),
                  //     ],
                  //   ),
                  // ),
                  const SizedBox(
                    height: 25,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: SizedBox(
                      width: 170,
                      child: Material(
                        elevation: 5,
                        color: Theme.of(context).primaryColor,
                        borderRadius: BorderRadius.circular(15.0),
                        child: MaterialButton(
                          // minWidth: MediaQuery.of(context).size.width * 1,
                          height: 70,
                          onPressed: () async {
                            if (clickEventIsFor == 'addILMViaPole') {
                              EasyLoading.show(
                                status: '',
                                dismissOnTap: false,
                              );

                              await _service.getDeviceDataService(
                                  ref, context, poleName, 1, "scanAPole");
                              await EasyLoading.dismiss();
                              if (context.mounted) {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => PoleDetails(),
                                  ),
                                );
                              }
                            } else {
                              ref.read(bottomMenuStateProvider.state).state = 0;
                              await Navigator.of(context)
                                  .pushNamedAndRemoveUntil(homeRoute,
                                      (Route<dynamic> route) => false);
                            }
                          },
                          child: Stack(
                            alignment: Alignment.centerLeft,
                            children: [
                              Text("Done",
                                  style: TextStyle(
                                      color: Theme.of(context).canvasColor,
                                      fontSize: 16)),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(7),
                                    decoration: BoxDecoration(
                                        color: Theme.of(context).cardColor,
                                        shape: BoxShape.circle),
                                    child: Icon(Icons.check,
                                        color: Theme.of(context).canvasColor),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    },
  );
}

void showGifDialog(BuildContext context) {
  List<Map<String, dynamic>> gifs = [
    {"path": Assets.ilmOutsideGif, "name": "ILM Outside"},
    {"path": Assets.ilmInsideGif, "name": "ILM Inside"},
    {"path": Assets.ccmsPanelGif, "name": "CCMS Panel"},
    {"path": Assets.lumiHubOutsideGif, "name": "LumiHub Outside"},
    {"path": Assets.lumiHubInsideGif, "name": "LumiHub Inside"}
  ];
  showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: SizedBox(
            width: double.maxFinite,
            height: 250,
            child: FlutterCarousel.builder(
              options: FlutterCarouselOptions(
                floatingIndicator: false,
                disableCenter: true,
                reverse: false,
                height: 55.0,
                showIndicator: true,
                slideIndicator: CircularSlideIndicator(
                    slideIndicatorOptions: SlideIndicatorOptions(
                        currentIndicatorColor:
                            Theme.of(context).secondaryHeaderColor,
                        indicatorRadius: 5,
                        indicatorBorderColor:
                            Theme.of(context).secondaryHeaderColor)),
              ),
              itemCount: gifs.length,
              itemBuilder: (BuildContext context, int index, int itemCount) {
                final gif = gifs[index];
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 5.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        gif['name']!,
                        style: TextStyle(
                          fontSize: 16,
                          color: Theme.of(context).secondaryHeaderColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Image.file(gif['path']!),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      });
}

infoDivider(BuildContext context) =>
    Divider(color: Theme.of(context).cardColor, thickness: .1);
Widget lineDesign(BuildContext context, title, value) {
  return Container(
    margin: const EdgeInsets.only(
      left: 5,
    ),
    child: SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(children: [
        SizedBox(
          width: 100,
          child: Text(
            title,
            style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor),
          ),
        ),
        Text(' :         ',
            style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
                fontSize: 15)),
        SizedBox(
          child: Text(value ?? '',
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                  fontSize: 15)),
        ),
      ]),
    ),
  );
}

userAlert(WidgetRef ref, BuildContext context, String msg, String deviceType,
    String clickEventIsFor) async {
  final cameras = await availableCameras();
  final firstCamera = cameras.first;
  String currentClickEvent = ref.watch(deviceController).clickeventIsFor;
  QRViewController? qrViewController = ref
      .read(ref.watch(isSecondTime) ? qrController2 : qrController)
      .qrViewController;
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return AlertDialog(
        backgroundColor: Theme.of(context).dialogTheme.backgroundColor,
        contentPadding: const EdgeInsets.all(20),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Lottie.file(Assets.userAlertLottie, height: 150, width: 150),
            const SizedBox(height: 20),
            Text(
              msg,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context, rootNavigator: true).pop();
                    if (qrViewController != null) {
                      qrViewController.resumeCamera();
                    }
                  },
                  child: Text("Cancel",
                      style: TextStyle(
                          color: Theme.of(context).unselectedWidgetColor)),
                ),
                OutlinedButton(
                  style: OutlinedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20.0),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 50),
                      side: BorderSide(color: Theme.of(context).cardColor),
                      backgroundColor: Theme.of(context).cardColor),
                  onPressed: () {
                    Navigator.of(context, rootNavigator: true).pop();

                    if (deviceType == "ILM") {
                      if (clickEventIsFor == "addILM" ||
                          clickEventIsFor == "addILMViaPole") {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => TakePictureScreen(
                                camera: firstCamera,
                                clickeventIsFor: currentClickEvent),
                          ),
                        );
                      } else {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ScanSelectionPage(),
                          ),
                        );
                      }
                    } else {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => TakePictureScreen(
                              camera: firstCamera,
                              clickeventIsFor: currentClickEvent),
                        ),
                      );
                    }
                  },
                  child: Text(
                    "OK",
                    style: TextStyle(color: Theme.of(context).canvasColor),
                  ),
                )
              ],
            ),
          ],
        ),
      );
    },
  );
}

// userAlert(WidgetRef ref, BuildContext context, msg, deviceType,
//     clickEventIsFor) async {
//   final cameras = await availableCameras();
//   final firstCamera = cameras.first;
//   String clickeventIsFor = ref.watch(deviceController).clickeventIsFor;
//   QRViewController? qrViewController = ref.read(qrController).qrViewController;
//   if (context.mounted) {
//     await CoolAlert.show(
//         //closeOnConfirmBtnTap: false,
//         context: context,
//         barrierDismissible: false,
//         type: CoolAlertType.confirm,
//         text: msg,
//         //textTextStyle: TextStyle(color: Theme.of(context).secondaryHeaderColor),
//         title: "",
//         lottieAsset: 'assets/animation/userAlert.json',
//         onConfirmBtnTap: () async => {
//               Navigator.of(context, rootNavigator: true).pop(),
//               // if (qrViewController != null) {qrViewController.resumeCamera()},
//               if (deviceType == "ILM")
//                 {
//                   clickEventIsFor == "addILM" ||
//                           clickEventIsFor == "addILMViaPole"
//                       ? Navigator.push(
//                           context,
//                           MaterialPageRoute(
//                             builder: (context) => TakePictureScreen(
//                                 camera: firstCamera,
//                                 clickeventIsFor: clickeventIsFor),
//                           ),
//                         )
//                       : Navigator.push(
//                           context,
//                           MaterialPageRoute(
//                             builder: (context) => const ScanSelectionPage(),
//                           ),
//                         )
//                 }
//               else
//                 {
//                   await Navigator.of(context).push(
//                     MaterialPageRoute(
//                       builder: (context) => TakePictureScreen(
//                           camera: firstCamera,
//                           clickeventIsFor: clickeventIsFor),
//                     ),
//                   )
//                 }
//             },
//         onCancelBtnTap: () => {
//               Navigator.of(context, rootNavigator: true).pop(),
//               if (qrViewController != null) {qrViewController.resumeCamera()}
//             },
//         confirmBtnColor: Theme.of(context).cardColor,
//         backgroundColor: Theme.of(context).dialogBackgroundColor);
//   }
// }

// lampRepUserAlert(
//     WidgetRef ref, BuildContext context, msg, clickEventIsFor) async {
//   final cameras = await availableCameras();
//   final firstCamera = cameras.first;
//   String clickeventIsFor = ref.watch(deviceController).clickeventIsFor;
//   QRViewController? qrViewController = ref.read(qrController).qrViewController;
//   if (context.mounted) {
//     await CoolAlert.show(
//         context: context,
//         barrierDismissible: false,
//         type: CoolAlertType.confirm,
//         text: msg,
//         //textTextStyle: TextStyle(color: Theme.of(context).secondaryHeaderColor),
//         title: "",
//         lottieAsset: 'assets/animation/userAlert.json',
//         onConfirmBtnTap: () async => {
//               Navigator.of(context, rootNavigator: true).pop(),
//               if (clickEventIsFor == "lampOnlyReplace")
//                 {
//                   // if (qrViewController != null)
//                   //   {qrViewController.resumeCamera()},
//                   Navigator.push(
//                     context,
//                     MaterialPageRoute(
//                       builder: (context) => TakePictureScreen(
//                           camera: firstCamera,
//                           clickeventIsFor: clickeventIsFor),
//                     ),
//                   )
//                 }
//               else
//                 {
//                   // if (qrViewController != null)
//                   //   {qrViewController.resumeCamera()},
//                   await ref
//                       .read(deviceController)
//                       .lampOrILMScanForReplace("ilm"),
//                   if (context.mounted)
//                     {
//                       await Navigator.of(context).push(MaterialPageRoute(
//                         builder: (context) => ScanQRWithCustomScreen(
//                             firstOrSecond: 2, clickEventIsFor: clickEventIsFor),
//                       ))
//                     }
//                   // await ref
//                   //     .read(deviceController)
//                   //     .scanDevice(ref, context, 2, clickEventIsFor),
//                 }
//             },
//         onCancelBtnTap: () => {
//               Navigator.of(context, rootNavigator: true).pop(),
//               if (qrViewController != null) {qrViewController.resumeCamera()}
//             },
//         confirmBtnColor: Theme.of(context).cardColor,
//         backgroundColor: Theme.of(context).dialogBackgroundColor);
//   }
// }

repUserAlert(WidgetRef ref, BuildContext context, msg, clickEventIsFor) async {
  final cameras = await availableCameras();
  final firstCamera = cameras.first;
  String clickeventIsFor = ref.watch(deviceController).clickeventIsFor;
  QRViewController? qrViewController = ref
      .read(ref.watch(isSecondTime) ? qrController2 : qrController)
      .qrViewController;
  if (context.mounted) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).dialogTheme.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Lottie.file(Assets.userAlertLottie, width: 150, height: 150),
              const SizedBox(height: 20),
              Text(
                msg,
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: [
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              TextButton(
                onPressed: () {
                  Navigator.of(context, rootNavigator: true).pop();
                  if (qrViewController != null) {
                    qrViewController.resumeCamera();
                  }
                },
                child: Text("Cancel",
                    style: TextStyle(
                        color: Theme.of(context).unselectedWidgetColor)),
              ),
              const SizedBox(width: 50),
              OutlinedButton(
                style: OutlinedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20.0),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 50),
                    side: BorderSide(color: Theme.of(context).cardColor),
                    backgroundColor: Theme.of(context).cardColor),
                onPressed: () {
                  Navigator.of(context, rootNavigator: true).pop();
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => TakePictureScreen(
                          camera: firstCamera,
                          clickeventIsFor: clickeventIsFor),
                    ),
                  );
                },
                child: Text(
                  "OK",
                  style: TextStyle(color: Theme.of(context).canvasColor),
                ),
              ),
            ]),
          ],
        );
      },
    );
  }
}

// repUserAlert(WidgetRef ref, BuildContext context, msg, clickEventIsFor) async {
//   final cameras = await availableCameras();
//   final firstCamera = cameras.first;
//   String clickeventIsFor = ref.watch(deviceController).clickeventIsFor;
//   QRViewController? qrViewController = ref.read(qrController).qrViewController;
//   if (context.mounted) {
//     await CoolAlert.show(
//         context: context,
//         barrierDismissible: false,
//         type: CoolAlertType.confirm,
//         text: msg,
//         //textTextStyle: TextStyle(color: Theme.of(context).secondaryHeaderColor),
//         title: "",
//         lottieAsset: 'assets/animation/userAlert.json',
//         onConfirmBtnTap: () => {
//               Navigator.of(context, rootNavigator: true).pop(),
//               Navigator.push(
//                 context,
//                 MaterialPageRoute(
//                   builder: (context) => TakePictureScreen(
//                       camera: firstCamera, clickeventIsFor: clickeventIsFor),
//                 ),
//               )
//             },
//         onCancelBtnTap: () => {
//               Navigator.of(context, rootNavigator: true).pop(),
//               if (qrViewController != null) {qrViewController.resumeCamera()}
//             },
//         confirmBtnColor: Theme.of(context).cardColor,
//         backgroundColor: Theme.of(context).dialogBackgroundColor);
//   }
// }

userAlertWithNoAction(WidgetRef ref, BuildContext context, String msg) async {
  QRViewController? qrViewController = ref
      .read(ref.watch(isSecondTime) ? qrController2 : qrController)
      .qrViewController;
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return PopScope(
        canPop: false,
        child: AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Lottie.file(
                Assets.userAlertLottie,
                height: 150,
                width: 150,
              ),
              const SizedBox(height: 15),
              Text(
                'Alert..!',
                style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 20),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 15),
              Text(msg, textAlign: TextAlign.center),
            ],
          ),
          actions: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20.0),
                    ),
                    side: BorderSide(color: Theme.of(context).cardColor),
                    padding: const EdgeInsets.symmetric(horizontal: 50),
                    backgroundColor: Theme.of(context).cardColor,
                  ),
                  onPressed: () {
                    Navigator.of(context, rootNavigator: true).pop();
                    if (qrViewController != null) {
                      qrViewController.resumeCamera();
                    }
                  },
                  child: Text(
                    "OK",
                    style: TextStyle(color: Theme.of(context).canvasColor),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    },
  );
}

// userAlertWithNoAction(WidgetRef ref, context, msg) async {
//   QRViewController? qrViewController = ref.read(qrController).qrViewController;
//   await CoolAlert.show(
//       context: context,
//       barrierDismissible: false,
//       type: CoolAlertType.error,
//       text: msg,
//       //textTextStyle: TextStyle(color: Theme.of(context).secondaryHeaderColor),
//       title: "Alert..!",
//       lottieAsset: 'assets/animation/userAlert.json',
//       onConfirmBtnTap: () => {
//             Navigator.of(context, rootNavigator: true).pop(),
//             if (qrViewController != null) {qrViewController.resumeCamera()}
//           },
//       showCancelBtn: false,
//       confirmBtnColor: Theme.of(context).cardColor,
//       backgroundColor: Theme.of(context).dialogBackgroundColor);
// }

poleVaultAlert(WidgetRef ref, context, msg) async {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return AlertDialog(
        backgroundColor: Theme.of(context).dialogTheme.backgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Lottie.file(Assets.userAlertLottie, width: 150, height: 150),
            const SizedBox(height: 20),
            Text(
              msg,
              style: const TextStyle(fontSize: 18),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          Row(mainAxisAlignment: MainAxisAlignment.center, children: [
            TextButton(
              onPressed: () {
                Navigator.of(context, rootNavigator: true).pop();
              },
              child: Text("Cancel",
                  style: TextStyle(
                      color: Theme.of(context).unselectedWidgetColor)),
            ),
            const SizedBox(width: 40),
            OutlinedButton(
              style: OutlinedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 50),
                  side: BorderSide(color: Theme.of(context).cardColor),
                  backgroundColor: Theme.of(context).cardColor),
              onPressed: () {
                ref.read(deviceController).poleVaultLaunchUrl();
                Navigator.of(context, rootNavigator: true).pop();
              },
              child: Text(
                "Install",
                style: TextStyle(color: Theme.of(context).canvasColor),
              ),
            ),
          ]),
        ],
      );
    },
  );
}
// poleVaultAlert(WidgetRef ref, context, msg) async => await CoolAlert.show(
//     context: context,
//     barrierDismissible: false,
//     type: CoolAlertType.error,
//     text: msg,
//     //textTextStyle: TextStyle(color: Theme.of(context).secondaryHeaderColor),
//     title: " ",
//     lottieAsset: 'assets/animation/userAlert.json',
//     confirmBtnText: "Install",
//     cancelBtnText: "Cancel",
//     onConfirmBtnTap: () => {
//           ref.read(deviceController).poleVaultLaunchUrl(),
//           Navigator.of(context, rootNavigator: true).pop()
//         },
//     onCancelBtnTap: () => Navigator.of(context, rootNavigator: true).pop(),
//     showCancelBtn: true,
//     confirmBtnColor: Theme.of(context).cardColor,
//     backgroundColor: Theme.of(context).dialogBackgroundColor);

lampRemoveAlert(WidgetRef ref, BuildContext context, String msg) async {
  final cameras = await availableCameras();
  final firstCamera = cameras.first;
  String clickeventIsFor = ref.watch(deviceController).clickeventIsFor;

  if (context.mounted) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Lottie.file(
                  Assets.userAlertLottie,
                  height: 150,
                  width: 150,
                ),
                const SizedBox(height: 15),
                Text(msg, textAlign: TextAlign.center),
              ],
            ),
            actions: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context, rootNavigator: true).pop();
                    },
                    child: Text("Cancel",
                        style: TextStyle(
                            color: Theme.of(context).unselectedWidgetColor)),
                  ),
                  OutlinedButton(
                    style: OutlinedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20.0),
                      ),
                      side: BorderSide(color: Theme.of(context).indicatorColor),
                      padding: const EdgeInsets.symmetric(horizontal: 40),
                      backgroundColor: Theme.of(context).indicatorColor,
                    ),
                    onPressed: () {
                      Navigator.of(context, rootNavigator: true).pop();
                      ref
                          .read(deviceController)
                          .isLocationFetchingComplete(true);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => TakePictureScreen(
                            camera: firstCamera,
                            clickeventIsFor: clickeventIsFor,
                          ),
                        ),
                      );
                    },
                    child: Text(
                      "Remove",
                      style: TextStyle(color: Theme.of(context).canvasColor),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}

// lampRemoveAlert(WidgetRef ref, BuildContext context, msg) async {
//   final cameras = await availableCameras();
//   final firstCamera = cameras.first;
//   String clickeventIsFor = ref.watch(deviceController).clickeventIsFor;
//   if (context.mounted) {
//     await CoolAlert.show(
//         context: context,
//         barrierDismissible: false,
//         type: CoolAlertType.error,
//         text: msg,
//         // textTextStyle: TextStyle(color: Theme.of(context).secondaryHeaderColor),
//         title: " ",
//         lottieAsset: 'assets/animation/userAlert.json',
//         confirmBtnText: "Remove",
//         cancelBtnText: "Cancel",
//         onConfirmBtnTap: () => {
//               Navigator.of(context, rootNavigator: true).pop(),
//               Navigator.push(
//                 context,
//                 MaterialPageRoute(
//                   builder: (context) => TakePictureScreen(
//                       camera: firstCamera, clickeventIsFor: clickeventIsFor),
//                 ),
//               ),
//             },
//         onCancelBtnTap: () => Navigator.of(context, rootNavigator: true).pop(),
//         showCancelBtn: true,
//         confirmBtnColor: Theme.of(context).indicatorColor,
//         backgroundColor: Theme.of(context).dialogBackgroundColor);
//   }
// }

tokenExpired(context, WidgetRef ref) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return PopScope(
        canPop: false,
        child: AlertDialog(
          icon: Container(
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4), topRight: Radius.circular(4)),
              color: Theme.of(context).primaryColor,
            ),
            width: double.infinity,
            child: Lottie.file(
              Assets.expiredLottie,
              height: 200,
              width: 200,
            ),
          ),
          iconPadding: const EdgeInsets.all(0),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 15),
              Text(ErrorMessages.sessionExpiredError,
                  textAlign: TextAlign.center),
            ],
          ),
          actions: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20.0),
                    ),
                    side: BorderSide(color: Theme.of(context).cardColor),
                    padding: const EdgeInsets.symmetric(horizontal: 50),
                    backgroundColor: Theme.of(context).cardColor,
                  ),
                  onPressed: () async {
                    Navigator.of(context, rootNavigator: true).pop();
                    ref.read(deviceController).emptyLocation();
                    ref.read(locationController).emptyCustomer();
                    ref.read(loginController).logoutLoadfalse();
                    removeUser();
                    ref.read(loginController).textFieldUpdate('', true);
                    ref.read(loginController).textFieldUpdate('', false);
                    Navigator.of(context).pushNamedAndRemoveUntil(
                        loginRoute, (Route<dynamic> route) => false);
                  },
                  child: Text(
                    "OK",
                    style: TextStyle(color: Theme.of(context).canvasColor),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    },
  );
}

// tokenExpired(context, WidgetRef ref) => CoolAlert.show(
//       context: context,
//       barrierDismissible: false,
//       type: CoolAlertType.error,
//       confirmBtnColor: Theme.of(context).primaryColor,
//       onConfirmBtnTap: () {
//         Navigator.of(context, rootNavigator: true).pop();
//         ref.read(loginController).logoutLoadfalse();
//         removeUser();
//         ref.read(loginController).textFieldUpdate('', true);
//         ref.read(loginController).textFieldUpdate('', false);
//         Navigator.of(context).pushNamedAndRemoveUntil(
//             loginRoute, (Route<dynamic> route) => false);
//       },
//       text: ErrorMessages.sessionExpiredError,
//       //textTextStyle: TextStyle(color: Theme.of(context).secondaryHeaderColor),
//       lottieAsset: 'assets/animation/expired.json',
//       backgroundColor: Theme.of(context).primaryColor,
//     );

// bool hasLocationPermission = false;
// Future<void> checkLocationPermission(context) async {
//   final ph.PermissionStatus permissionStatus =
//       await ph.Permission.location.status;

//   if (permissionStatus == ph.PermissionStatus.denied) {
//     hasLocationPermission = false;
//   } else {
//     hasLocationPermission = true;
//   }
// }

permissionRequiredAlert(context, msg) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return PopScope(
        canPop: false,
        child: AlertDialog(
          icon: Container(
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4), topRight: Radius.circular(4)),
              color: Theme.of(context).primaryColor,
            ),
            width: double.infinity,
            child: Lottie.file(
              Assets.expiredLottie,
              height: 200,
              width: 200,
            ),
          ),
          iconPadding: const EdgeInsets.all(0),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 15),
              Text(msg, textAlign: TextAlign.center),
            ],
          ),
          actions: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20.0),
                    ),
                    side: BorderSide(color: Theme.of(context).cardColor),
                    padding: const EdgeInsets.symmetric(horizontal: 50),
                    backgroundColor: Theme.of(context).cardColor,
                  ),
                  onPressed: () async {
                    Navigator.of(context, rootNavigator: true).pop();
                    await ph.openAppSettings();
                  },
                  child: Text(
                    "OK",
                    style: TextStyle(color: Theme.of(context).canvasColor),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    },
  );
}

// locationPermissionRequired(context, WidgetRef ref) => CoolAlert.show(
//       context: context,
//       barrierDismissible: false,
//       type: CoolAlertType.error,
//       confirmBtnColor: Theme.of(context).primaryColor,
//       onConfirmBtnTap: () async {
//         // await checkLocationPermission(context);
//         // Navigator.of(context, rootNavigator: true).pop();
//         // if (hasLocationPermission == false) {
//         // await ph.openAppSettings();
//         // } else {
//         //   ref.read(splashController).checkAppVersion(context, ref);
//         // }

//         Navigator.of(context, rootNavigator: true).pop();
//         await ph.openAppSettings();
//       },
//       text: ErrorMessages.enableLocationAlert,
//       // textTextStyle: TextStyle(color: Theme.of(context).secondaryHeaderColor),
//       lottieAsset: 'assets/animation/expired.json',
//       backgroundColor: Theme.of(context).primaryColor,
//     );

// showMessage(context, message, Color c) => Toast.show(message,
//     duration: Toast.lengthLong, backgroundColor: c, gravity: Toast.top);

// showToastMessage(context, message, Color c) =>
//     Toast.show(message, duration: 5, backgroundColor: c, gravity: Toast.bottom);

class BaseAlertDialog extends StatelessWidget {
  final String title;
  final String content;
  final String yes;
  final String no;
  final Function yesOnPressed;
  final Function noOnPressed;
  const BaseAlertDialog({
    Key? key,
    required this.title,
    required this.content,
    required this.yes,
    required this.no,
    required this.yesOnPressed,
    required this.noOnPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(title),
      content: Text(content),
      backgroundColor: secondaryColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      actions: <Widget>[
        TextButton(
          style: ButtonStyle(
              foregroundColor:
                  WidgetStateProperty.all(Theme.of(context).indicatorColor)),
          onPressed: () {
            yesOnPressed();
          },
          child: Text(yes),
        ),
        TextButton(
          style: ButtonStyle(
              foregroundColor:
                  WidgetStateProperty.all(Theme.of(context).hintColor)),
          onPressed: () {
            noOnPressed();
          },
          child: Text(no),
        ),
      ],
    );
  }
}

Future<void> updatePopup(BuildContext context, WidgetRef ref, String message,
    Map<String, dynamic> releaseNotesMap) async {
  double width = MediaQuery.of(context).size.width;
  bool _isExpanded = false;

  return await showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) {
      return StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(24.0)),
            ),
            insetPadding: const EdgeInsets.all(8.0),
            content: SizedBox(
              width: MediaQuery.of(context).size.width * 0.80,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(height: MediaQuery.of(context).size.height / 200),
                  Text(
                    message,
                    softWrap: true,
                    textAlign: TextAlign.start,
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w400,
                      color: Theme.of(context).secondaryHeaderColor,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Container(
                      padding: const EdgeInsets.all(5.0),
                      margin: const EdgeInsets.only(left: 3),
                      color: Theme.of(context)
                          .unselectedWidgetColor
                          .withOpacity(0.1),
                      width: width / 1.02,
                      child: Theme(
                        data: Theme.of(context).copyWith(
                          expansionTileTheme: ExpansionTileThemeData(
                            collapsedTextColor: Theme.of(context).cardColor,
                            collapsedShape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(0),
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(0),
                            ),
                          ),
                        ),
                        child: ExpansionTile(
                          title: const Text("Release Notes"),
                          onExpansionChanged: (bool expanded) {
                            setState(() => _isExpanded = expanded);
                          },
                          children: [
                            RichText(
                              text: TextSpan(
                                style: TextStyle(
                                  fontSize: 15.0,
                                  color: Theme.of(context).secondaryHeaderColor,
                                ),
                                children: [
                                  for (String title
                                      in releaseNotesMap.keys) ...[
                                    TextSpan(
                                      text: '$title:\n',
                                      style: const TextStyle(
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    for (String message
                                        in releaseNotesMap[title]) ...[
                                      WidgetSpan(
                                        child: Padding(
                                          padding: const EdgeInsets.all(4.0),
                                          child: Icon(
                                            Icons.star,
                                            size: 12,
                                            color: Theme.of(context)
                                                .secondaryHeaderColor,
                                          ),
                                        ),
                                      ),
                                      TextSpan(
                                        text: " " + message + '\n',
                                        style: const TextStyle(fontSize: 10),
                                      ),
                                    ],
                                    if (releaseNotesMap.keys.last != title)
                                      const WidgetSpan(
                                        child: SizedBox(height: 25.0),
                                      ),
                                  ]
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      GestureDetector(
                        onTap: () {
                          Navigator.of(context, rootNavigator: true).pop();
                        },
                        child: Container(
                          width: 80,
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: Theme.of(context).primaryColor,
                              width: 1.0,
                            ),
                            borderRadius: BorderRadius.circular(15.0),
                          ),
                          child: Center(
                            child: Text(
                              'Skip',
                              style: TextStyle(
                                color: Theme.of(context).primaryColor,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 20),
                      GestureDetector(
                        onTap: () {
                          ref.read(loginController).launchUrl();
                        },
                        child: Container(
                          width: 80,
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            border: Border.all(
                              color: Theme.of(context).primaryColor,
                              width: 1.0,
                            ),
                            borderRadius: BorderRadius.circular(15.0),
                          ),
                          child: Center(
                            child: Text(
                              'Update',
                              style: TextStyle(
                                color: Theme.of(context).canvasColor,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      );
    },
  );
}
