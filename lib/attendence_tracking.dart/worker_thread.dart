import 'dart:async';
import 'dart:developer';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:geolocator/geolocator.dart';
import 'package:schnell_luminator/attendence_tracking.dart/user_tracking_service.dart';
import 'package:schnell_luminator/utils/session.dart';
import 'package:schnell_luminator/utils/utility.dart';
import 'package:shared_preferences/shared_preferences.dart';

@pragma('vm:entry-point')
void startCallback() {
  FlutterForegroundTask.setTaskHandler(LocationTaskHandler());
}

Future<void> initializeService() async {
  await startLocationTracking();
}

Future<void> startLocationTracking() async {
  final prefs = await SharedPreferences.getInstance();
  final startTime = DateTime.now().millisecondsSinceEpoch;
  await prefs.setInt('periodicTrackingStartTime', startTime);
  await FlutterForegroundTask.startService(
    notificationTitle: 'Tracking Location',
    notificationText: '',
    callback: startCallback,
  );
}

Future<void> stopLocationTracking() async {
  bool isServiceRunning = await FlutterForegroundTask.isRunningService;
  log('$isServiceRunning');
  log("Location tracking service cancelled");
  await FlutterForegroundTask.stopService();
}

class LocationTaskHandler extends TaskHandler {
  @override
  Future<void> onStart(DateTime timestamp, TaskStarter starter) async {
    log('Foreground task started');
  }

  @override
  void onRepeatEvent(DateTime timestamp) async {
    final prefs = await SharedPreferences.getInstance();
    final startTime = prefs.getInt('periodicTrackingStartTime') ?? 0;
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    final elapsedTime = currentTime - startTime;

    //auto stop tracking after 8 hrs
    if (elapsedTime >= 8 * 60 * 60 * 1000) {
      await FlutterForegroundTask.stopService();
      return;
    }
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      final time = DateTime.now().millisecondsSinceEpoch.toString();
      if (serviceEnabled) {
        final position = await Geolocator.getCurrentPosition(
          locationSettings:
              const LocationSettings(accuracy: LocationAccuracy.high),
        );
        final latitude = position.latitude;
        final longitude = position.longitude;

        log("Fetched location (hourly): $latitude, $longitude at $time");

        await storeLocationLocally(time,
            lat: latitude, long: longitude, locationEnabled: true);
      } else {
        log("Location services disabled at $time");
        await storeLocationLocally(time, locationEnabled: false);
      }
      Utility.isConnected().then((value) async {
        if (value) {
          //if internet available else location details just stored locally
          await UserTrackingService().pushLocationToUserTelemetry();
        }
      });
    } catch (e) {
      log("Location error: $e");
    }
  }

  @override
  Future<void> onDestroy(DateTime timestamp, bool isTimeout) async {
    await _checkAndRestartServiceIfNeeded();
    log('Foreground task stopped');
  }

  Future<void> _checkAndRestartServiceIfNeeded() async {
    await Future.delayed(const Duration(seconds: 3));
    // Check if app is still running but service was killed
    bool isServiceRunning = await FlutterForegroundTask.isRunningService;
    // bool shouldBeRunning = /* get from your app state/preferences */;

    if (!isServiceRunning) {
      await startLocationTracking();
    }
  }
}

// final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
//     FlutterLocalNotificationsPlugin();

// Future<void> initializeNotifications() async {
//   const AndroidInitializationSettings initializationSettingsAndroid =
//       AndroidInitializationSettings('@mipmap/ic_launcher');

//   const InitializationSettings initializationSettings = InitializationSettings(
//     android: initializationSettingsAndroid,
//   );

//   await flutterLocalNotificationsPlugin.initialize(initializationSettings);
// }

// Future<void> showHourlyNotification() async {
//   const AndroidNotificationDetails androidPlatformChannelSpecifics =
//       AndroidNotificationDetails(
//     'hourly_channel_id',
//     'Hourly Notification',
//     channelDescription: 'Notifies every hour',
//     importance: Importance.high,
//     priority: Priority.high,
//     ongoing: true, // Optional: make it persistent
//   );

//   const NotificationDetails platformChannelSpecifics =
//       NotificationDetails(android: androidPlatformChannelSpecifics);

//   await flutterLocalNotificationsPlugin.show(
//     0,
//     'Hourly Alert',
//     'This is your hourly notification at ${DateTime.now().toLocal()}',
//     platformChannelSpecifics,
//   );
// }

// void scheduleHourlyAlarm() {
//   AndroidAlarmManager.oneShot(
//     const Duration(minutes: 1),
//     123456, // Alarm ID
//     alarmCallback,
//     wakeup: true,
//     alarmClock: true,
//     exact: true,
//     allowWhileIdle: true, // Ensures it triggers even in Doze mode
//     rescheduleOnReboot: true,
//   );
//   // AndroidAlarmManager.periodic(
//   //   const Duration(minutes: 1),
//   //   123456, // Alarm ID
//   //   alarmCallback,
//   //   wakeup: true,
//   //   exact: true,
//   //   allowWhileIdle: true,
//   //   rescheduleOnReboot: true,
//   // );
// }

// @pragma('vm:entry-point') // Required!
// Future<void> alarmCallback() async {
//   showHourlyNotification();
//   try {
//     bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
//     LocationPermission permission = await Geolocator.checkPermission();
//     if (permission == LocationPermission.denied) {
//       permission = await Geolocator.requestPermission();
//     }
//     if (!serviceEnabled ||
//         permission == LocationPermission.denied ||
//         permission == LocationPermission.deniedForever) {
//       print("Location not available or permission denied.");
//       return;
//     }

//     Position position = await Geolocator.getCurrentPosition(
//       desiredAccuracy: LocationAccuracy.high,
//     );
//     final latitude = position.latitude;
//     final longitude = position.longitude;
//     final timestamp = DateTime.now().millisecondsSinceEpoch.toString();

//     print('Alarm fetched location: $latitude, $longitude, $timestamp');
//     // Create instance of your service
//     final userTrackingService = UserTrackingService();

//     // Store and push location data
//     await storeLocationLocally(timestamp, latitude, longitude);
//     await userTrackingService.pushLocationToUserTelemetry();
//   } catch (e) {
//     print("Error getting location: $e");
//   }
//   scheduleHourlyAlarm();
// }

// const int alarmId = 8888;
// Future<void> scheduleHourlyRestart() async {
//   await AndroidAlarmManager.cancel(alarmId); // Clean old ones

//   await AndroidAlarmManager.periodic(
//     const Duration(minutes: 10),
//     alarmId,
//     alarmCallback,
//     exact: true,
//     wakeup: true,
//     rescheduleOnReboot: true,
//   );
// }

// Future<void> initializeService() async {
//   final service = FlutterBackgroundService();

//   // Setup notification channel for Android
//   const AndroidNotificationChannel channel = AndroidNotificationChannel(
//     'location_tracker_channel', // id
//     'Location Tracker', // title
//     description: 'Keeps track of your location in background', // description
//     importance: Importance.high,
//   );

//   final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
//       FlutterLocalNotificationsPlugin();

//   if (Platform.isAndroid) {
//     await flutterLocalNotificationsPlugin
//         .resolvePlatformSpecificImplementation<
//             AndroidFlutterLocalNotificationsPlugin>()
//         ?.createNotificationChannel(channel);
//   }

//   // Configure the service
//   await service.configure(
//     androidConfiguration: AndroidConfiguration(
//       onStart: onStart,
//       autoStart: true,
//       isForegroundMode: true,
//       notificationChannelId: 'location_tracker_channel',
//       initialNotificationTitle: 'Location Tracker Running',
//       initialNotificationContent: 'Initializing...',
//       foregroundServiceNotificationId: 888,
//       foregroundServiceTypes: [AndroidForegroundType.location],
//       autoStartOnBoot: true,
//     ),
//     iosConfiguration: IosConfiguration(),
//   );
//   await service.startService();
//   await scheduleHourlyRestart();
//   print('alarm scheduled');
// }

// @pragma('vm:entry-point')
// void alarmCallback() async {
//   final service = FlutterBackgroundService();
//   service.invoke('fetchLocation');
//   // await scheduleNextAlarm();
// }

// @pragma('vm:entry-point')
// void onStart(ServiceInstance service) async {
//   DartPluginRegistrant.ensureInitialized();

//   // For Android, set as foreground service with notification
//   if (service is AndroidServiceInstance) {
//     service.on('setAsForeground').listen((event) {
//       service.setAsForegroundService();
//       service.setForegroundNotificationInfo(
//         title: "Location Tracker",
//         content: "Tracking user location in background",
//       );
//       // Set auto start on boot
//       service.setAutoStartOnBootMode(true);
//     });

//     service.on('setAsBackground').listen((event) {
//       service.setAsBackgroundService();
//     });
//   }

//   service.on('fetchLocation').listen((event) async {
//     try {
//       bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
//       LocationPermission permission = await Geolocator.checkPermission();
//       if (permission == LocationPermission.denied) {
//         permission = await Geolocator.requestPermission();
//       }
//       if (!serviceEnabled ||
//           permission == LocationPermission.denied ||
//           permission == LocationPermission.deniedForever) {
//         print("Location not available or permission denied.");
//         return;
//       }

//       Position position = await Geolocator.getCurrentPosition(
//         desiredAccuracy: LocationAccuracy.high,
//       );
//       final latitude = position.latitude;
//       final longitude = position.longitude;
//       final timestamp = DateTime.now().millisecondsSinceEpoch.toString();

//       print('Alarm fetched location: $latitude, $longitude, $timestamp');
//       // Create instance of your service
//       final userTrackingService = UserTrackingService();

//       // Store and push location data
//       await storeLocationLocally(timestamp, latitude, longitude);
//       await userTrackingService.pushLocationToUserTelemetry();
//     } catch (e) {
//       print("Error getting location: $e");
//     }
//   });
// }

// Future<void> stopLocationTracking() async {
//   await AndroidAlarmManager.cancel(123456);

//   // final service = FlutterBackgroundService();
//   // service.invoke("stopService");

//   print("Location tracking service cancelled");
// }
