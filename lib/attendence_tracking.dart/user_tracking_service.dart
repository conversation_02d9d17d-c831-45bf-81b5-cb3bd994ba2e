import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:schnell_luminator/utils/dio_client.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../utils/constants.dart';

class UserTrackingService {
  Future<dynamic> pushLocationToUserTelemetry() async {
    final sharedPreference = await SharedPreferences.getInstance();
    String? userId = sharedPreference.getString("userId");
    log('get stored userId: $userId');
    var storedLocation = sharedPreference.getStringList("locations");
    Dio dio = DioClient.dio;
    List<String> locationsToRemove = [];

    if (storedLocation != null) {
      for (var location in List.from(storedLocation)) {
        try {
          var decodedLocation = jsonDecode(location);
          final response = await dio.post(
            '$ticketURL/api/plugins/telemetry/USER/$userId/timeseries/ANY',
            options: Options(
              headers: {
                'accept': 'application/json',
                'Content-Type': 'application/json',
                // 'X-Authorization': 'Bearer $token',
              },
            ),
            data: decodedLocation,
          );
          log('Location update response: ${response.statusCode}');
          if (response.statusCode == 200) {
            locationsToRemove.add(location);
            log('Location successfully updated to user telemetry and marked for removal');
          }
        } catch (e) {
          if (e is DioError) {
            if (e.error == 'Session expired. Please login again.') {
              return 'refresht token expired';
            }
          }
        }
      }
      // Remove successfully sent locations from storedLocation
      storedLocation
          .removeWhere((location) => locationsToRemove.contains(location));
      await sharedPreference.setStringList("locations", storedLocation);
      log("Remaining stored locations: $storedLocation");
      log("*** Locations marked for removal: $locationsToRemove");
    }
  }

//   Future<dynamic> ppeImageUploadService(base64Image, timestamp) async {
//     // var headers = {'token': token};
//     var data = FormData.fromMap(
//         {'ppeImg': base64Image, 'ts': timestamp, 'imgExtension': 'JPG'});
//     try {
//       var dio = DioClient.dio;
//       var response = await dio.request(
//         '$baseURL/api/login/ppe',
//         options: Options(
//           method: 'POST',
//           // headers: headers,
//         ),
//         data: data,
//       );

//       if (response.statusCode == 200) {
//         log(response.data);

//         return 1;
//       } else {
//         log(response.statusMessage.toString());
//         return 0;
//       }
//     } catch (e) {
//       if (e is DioError) {
//         if (e.error == 'Session expired. Please login again.') {
//           return 401;
//         } else {
//           return ErrorMessages.serverTimeOutError;
//         }
//       }
//     }
//   }
}
