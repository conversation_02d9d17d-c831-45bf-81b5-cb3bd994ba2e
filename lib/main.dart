import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:schnell_luminator/luminator_app.dart';
import 'utils/custom_loading.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]).then(
    (_) {
      HttpOverrides.global = MyHttpOverrides();
      runApp(const ProviderScope(
        child: LuminatorApp(),
      ));
      configLoading();
    },
  );
}

void configLoading() {
  EasyLoading.instance
    ..indicatorType = EasyLoadingIndicatorType.ring
    ..loadingStyle = EasyLoadingStyle.light
    ..indicatorSize = 45.0
    ..radius = 10.0
    ..textColor = Colors.green
    ..maskColor = Colors.green.withOpacity(0.5)
    ..userInteractions = false
    ..dismissOnTap = false
    ..customAnimation = CustomAnimation()
    ..progressColor = Colors.green
    ..backgroundColor = const Color.fromARGB(255, 144, 184, 210)
    ..indicatorColor = Colors.green;
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

// Workmanager().initialize(
//   callbackDispatcher,
//   isInDebugMode: true, // Set to false in production
// );
// @pragma('vm:entry-point')
// void callbackDispatcher() {
//   Workmanager().executeTask((task, inputData) async {
//     print("enetered into the task");
//     switch (task) {
//       case 'fetchHourlyLocation':
//         try {
//           final Location location = await FlLocation.getLocation();
//           print('Location: ${location.toJson()}');
//           // Position position = await Geolocator.getCurrentPosition(
//           //   desiredAccuracy: LocationAccuracy.high,
//           // );
//           final latitude = location.latitude;
//           final longitude = location.longitude;
//           final timestamp = DateTime.now().millisecondsSinceEpoch.toString();

//           print(
//               'Workmanager fetched location: $latitude, $longitude, $timestamp');

//           // Create instance of your service (ensure it's properly initialized)
//           final userTrackingService = UserTrackingService();

//           // Store and push location data
//           await storeLocationLocally(timestamp, latitude, longitude);
//           await userTrackingService.pushLocationToUserTelemetry();
//         } catch (e) {
//           print("Error fetching location in Workmanager: $e");
//         }
//         break;
//     }
//     return Future.value(true);
//   });
// }
