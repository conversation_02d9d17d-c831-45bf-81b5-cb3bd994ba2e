import 'package:dio/dio.dart';

void main() {
  api();
}

Future<void> api() async {
  Dio dio = Dio();
  var token =
      "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.nAarau305K_PWGZBzBGh-Ul913T2mFXzTMaPjILfCDPfMFRHRSPKZqRr2aTggPMvEZf0Mnk4msP9M3hlocL41w";
  var data = {
    {
      "ilm": {"name": "ILMA93"},
      "latitude": 11.0810354,
      "longitude": 76.9891761,
      "accuracy": 12.7,
      "landmark": "Saravanampatti, Coimbatore, Tamil Nadu, India, 641006,",
      "wardName": "ooty1-1",
      "zoneName": "ooty1",
      "region": "OOTY",
      "installedOn": 1706852332,
      "installedBy": "<EMAIL>",
      "customerId": "af085e20-7a10-11ed-a1d5-c72869dcd693",
      "wardId": "95fdb720-80fc-11ed-82ca-f9d13b8ff550",
      "lightPointId": "",
      "poleId": "",
      "lamp": {
        "name": "ILM548999",
        "type": "lamp",
        "lampWatts": 0120,
        "manufacturer": "BI",
        "lampType": "02",
        "dimmable": "01",
        "year": "2022"
      }
    }
  };
  dio.options.headers["token"] = token;
  await dio.post('http://iotpro.io:8079/api/luminaire/maintain/',
      options: Options(contentType: 'application/json'), data: data);
}
